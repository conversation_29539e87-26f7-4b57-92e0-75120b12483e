import { BATTLE_PREVIEW_BG } from "../../common/constant/Constant";
import { BattleLevelType, ConditionType, ItemType, OreLandType, OreOperateType, PassengerBattleAnimation, TipsNotType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { anim<PERSON>elper } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../../model/common/ConditionObj";
import { OreHole } from "../../model/ore/OreHole";
import { OreLandNode } from "../../model/ore/OreLandNode";

const { ccclass } = cc._decorator;
const MoveTime = 0.09 //上移一行的时间
const LINECAP = 8 //视图显示(0 - LINECAP)行,总共LINECAP + 1行
const LINEVIEW = 9
const ROWVIEW = 6
const DEEPCAP = 5 //深度几位数
const ManyNum = 3 // 矿的个数>=ManyNum时显示min_icon_many_id

const MagicCfg = [
    { name: '1', propId: 14 },
    { name: '2', propId: 15 },
    { name: '3', propId: 16 },
    { name: '4', propId: 17 },
]
const CANTDAOJULIST = [ //不会被炸弹/钻头摧毁的类型
    OreLandType.BACK,
    OreLandType.BOSS,
    OreLandType.PURPLE,
    OreLandType.NEXT,
    OreLandType.SPECIAL,
    OreLandType.START,
]

const FloatAngle = 30           //钻石飘出偏移角度（+-FloatAngle）
const FloatBornTimeMax = 0.1    //钻石在FloatBornTimeMax内从中心发射
const FloatTime = 0.3           //所有钻石的borntime和飘出耗时之和
const FloatScaleMin = 1.0       //钻石飘出后最小scale
const FloatScaleMax = 1.6         //钻石飘出后最大scale
const FloatStopTime = 0.1      //钻石飘出后停顿时间
const FlyTime = 0.7            //钻石飞向钻石栏最大时间
const FlySpeed = 2000          //钻石飞向钻石栏速度
const EndScale = 0.8           //钻石飞到钻石栏最终缩放

//钻石飞出的位置从DiamondPos里取钻石个数个
const DiamondPos: { x, y }[] = [
    { x: 100, y: 180 },
    { x: -80, y: 130 },
    { x: 50, y: 100 },
    { x: -150, y: 60 },
    { x: 70, y: 40 },
    { x: -40, y: 20 },
    { x: 130, y: -10 },
    { x: -110, y: -20 },
    { x: 90, y: -80 },
    { x: -100, y: -100 },
    { x: 0, y: -70 },
    { x: -30, y: -150 },
    { x: 140, y: -130 },
    { x: 10, y: 160 },
]
/*
type showData = {
    x: number,
    y: number,
    type: OreLandType,
    cond: ConditionObj[],
    daojuList: {
        x: number,
        y: number,
    }[],
}
    */

@ccclass
export default class OrePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected mapViewNode_: cc.Node = null // path://root/mapView_n
    protected magicNode_: cc.Node = null // path://root/magic_n
    protected adNode_: cc.Node = null // path://root/magic_n/1/ad_n_be
    protected timeNode_: cc.Node = null // path://root/magic_n/1/time_n
    protected rockNode_: cc.Node = null // path://root/rock_n
    protected smokeNode_: cc.Node = null // path://root/smoke_n
    protected daojuNode_: cc.Node = null // path://root/daoju_n
    protected rewardNode_: cc.Node = null // path://root/reward_n
    protected rewardGroupNode_: cc.Node = null // path://root/rewardGroup_n
    protected daojuGroupNode_: cc.Node = null // path://root/daojuGroup_n
    protected rockGroupNode_: cc.Node = null // path://root/rockGroup_n
    protected smokeGroupNode_: cc.Node = null // path://root/smokeGroup_n
    protected makeNode_: cc.Node = null // path://root/make_be_n
    protected lineNode_: cc.Node = null // path://root/line_n
    protected itemNode_: cc.Node = null // path://root/item_n
    protected monsterNode_: cc.Node = null // path://root/monster_n
    protected specialNode_: cc.Node = null // path://root/special_n
    protected deepLineNode_: cc.Node = null // path://deepLine_n
    protected backNode_: cc.Node = null // path://back_be_n
    //@end

    private hole: OreHole & {} = null //这里hole是数据层的复制体，用于表现层的更新
    private level: number = 0
    private deep: number = 0
    private isMoving: boolean = false
    private curMagic: string = '1'
    /**
     * 切换map时（主要是进入特殊区域），如果上一次地块操作后但是视图没有更新完成，就会导致地块是黑的
     * 使用这个参数，如果上一次操作没有成功更新视图，那就在切换后更新一次
     */
    private _mustUpdateNow: () => void = null

    private lockMap = {}

    public listenEventMaps() {
        return [
            { [EventType.CHANGE_NUM_PROP]: this.bagChange },
            { [EventType.ORE_REFRESH_VIEW]: this.initView },
            { [EventType.ORE_MOVE]: this.moveRow }
        ]
    }

    public async onCreate(level: number) {
        this.setParam({ isAct: false })
        this.level = level
        this.hole = new OreHole().copy(gameHelper.ore.getOreHole(this.level))
    }

    public onEnter(level: number) {
        this.initView()

        if (this.hole.getRealDeep() < 2) {
            viewHelper.showPlanetTips("explore_guiText_22", TipsNotType.NORMAL_2)
        }
    }

    protected update(dt: number): void {
        this.timeNode_.active = gameHelper.ore.getNormalBreakMaxNum() > gameHelper.ore.getNormalBreakNum()
        this.timeNode_.Child('lb', cc.Label).string = ut.millisecondFormat(gameHelper.ore.getRecoverTime())

        // 看广告按钮
        // this.adNode_.active = gameHelper.ore.getNormalBreakNum() <= 0
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://root/make_be_n
    onClickMake(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('train/BagPnl')
    }

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://root/magic_n/1/ad_n_be
    onClickAd(event: cc.Event.EventTouch, data: string) {
        // viewHelper.showPnl("ore/OreWatchAdPnl")
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async onClickLand(it: cc.Node, viewData: OreLandNode) {
        if (this.isMoving) {
            viewHelper.showAlert('正在移动，请稍等')
            return
        }
        let row = viewData.getRow(), line = viewData.getLine()
        if (viewData["__lock"]) return

        let data = new OreLandNode().copy(gameHelper.ore.getOreNodeByPos(row, line, this.level), this.hole)

        let lock = (_lock, node = viewData) => {
            if (!node) return
            node["__lock"] = _lock
        }

        if (gameHelper.ore.getOreNodeByPos(data.getRow(), data.getLine(), this.level).isNone()) { return }
        //这里用这个让它可以连点
        if (!gameHelper.ore.canReach(data.getRow(), data.getLine(), this.level)) {
            viewHelper.showAlert('ore_tips_3')
            return
        }
        let type = data.getType()
        if (data.isMonster()) {
            lock(true)
            let succ = await this.showBattle(data)
            lock(false)
            if (!cc.isValid(this)) return
            if (!succ) return
            let brothers = data.getRelation()
            if (!!brothers && brothers.length > 0) {
                let nodes: OreLandNode[] = []
                let node = data
                if (brothers.length == 1) {
                    node = this.hole.getOreNodeByPos(brothers[0].row, brothers[0].line)
                }
                nodes = node.getRelation().map(m => { return this.hole.getOreNodeByPos(m.row, m.line) })
                nodes.push(node)
                nodes.forEach(m => {
                    this.operateByPos(m.getLine(), m.getRow())
                })
            }
            else {
                this.operateByPos(data.getLine(), data.getRow())
            }

        }
        else if (type == OreLandType.NEXT) {
            lock(true)
            let succ = await gameHelper.ore.operateLands(data, OreOperateType.NEXT, this.level)
            lock(false)
            if (!cc.isValid(this)) return
            if (succ) {
                this.hole.copy(gameHelper.ore.getOreHole(this.level))
                await this.moveLine()
                this.setMapMask()
                this.updateDeep()
                if (!cc.isValid(this)) return
            }
        }
        else if (type == OreLandType.SPECIAL || type == OreLandType.BACK) {
            lock(true)
            // 把之前未完成的更新做了
            this._mustUpdateNow?.()
            this._mustUpdateNow = null

            let succ = await gameHelper.ore.operateLands(data, OreOperateType.SPECIAL, this.level)
            lock(false)
            if (!cc.isValid(this)) return
            if (succ) {
                await this.hole.moveRow(type == OreLandType.BACK)
                await this.playSceneChange(it, type)

                this.updateDeep()
                if (!cc.isValid(this)) return
            }
        }
        else if (type == OreLandType.BOOM || type == OreLandType.DRILL) {
            let opType = type == OreLandType.BOOM ? OreOperateType.BOOM : OreOperateType.DRILL
            lock(true)
            let nodes = data.getDaojuNodes()
            nodes.forEach(({ x, y }) => {
                let n = viewData.getHole().getOreNodeByPos(x, y)
                lock(true, n)
            })
            let succ = await gameHelper.ore.operateLands(data, opType, this.level)
            lock(false)
            nodes.forEach(({ x, y }) => {
                let n = viewData.getHole().getOreNodeByPos(x, y)
                lock(false, n)
            })
            if (!cc.isValid(this)) return
            if (succ) {
                //数据同步在表现过程中做了
                await this.showEffects(opType, it, data)
                if (!cc.isValid(this)) return
            }
        }
        else {
            //判断数量
            let num = gameHelper.bag.getPropCountById(MagicCfg.find(e => e.name == this.curMagic).propId)
            if (data.getType() != OreLandType.NONE && num <= 0) {
                viewHelper.showAlert('ore_tips_1')
                return
            }
            if (type == OreLandType.BLACK) {
                viewHelper.showAlert('ore_tips_2')
                return
            }
            let opType
            if (this.curMagic == '1') {
                opType = OreOperateType.ONE
            }
            else if (this.curMagic == '2') {
                opType = OreOperateType.TWICE
            }
            viewHelper.hidePlanetTips()
            lock(true)
            let succ = await gameHelper.ore.operateLands(data, opType, this.level)
            lock(false)
            if (!cc.isValid(this)) return
            if (succ) {
                await this.showEffects(opType, it, data)
                if (!cc.isValid(this)) return
            }
        }
        this.updateMagic()
    }

    // todo 把单点修改变成视图层数据和视图节点一起修改,同时每次单点修改都改一次mask,同时修改deep
    private operateByPos(line: number, row: number) {
        let data = this.hole.getOreNodeByPos(row, line)?.copy(gameHelper.ore.getOreNodeByPos(row, line, this.level), this.hole)
        //快速点敲击+下一页会出现没有data的情况
        if (!data) return
        if (data.getType() == OreLandType.NONE && data.getRow() < ROWVIEW) {
            this.hole.refreshRealDeep()
        }
        this.updateByPos(line, row)
        this.setMapMask()
        this.updateDeep()
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView(reCopy: boolean = false) {
        if (reCopy) {
            this.hole = new OreHole().copy(gameHelper.ore.getOreHole(this.level))
        }
        this.adNode_.active = false
        let content = this.mapViewNode_.Child('content')
        let contentMonster = this.mapViewNode_.Child('content_monster')
        content.removeAndDestroyAllChildren()
        contentMonster.removeAndDestroyAllChildren()
        this.updateMap()
        this.updateDeep()
        this.updateMagic()
    }

    private updateMap() {
        let startNode = this.hole.startNode
        let content = this.mapViewNode_.Child('content')
        let contentMonster = this.mapViewNode_.Child('content_monster')
        let list = this.hole.getOreMap()
        this.deep = this.hole.getDeep()
        let maxDeep = -1
        //todo 这里需要根据startNode 设置初始的y
        let isDown = startNode.x >= ROWVIEW
        let dis = (this.itemNode_.height + this.lineNode_.Component(cc.Layout).spacingY) * ROWVIEW * (isDown ? 1 : 0)
        content.y = 518.5 + dis
        contentMonster.y = dis
        content.children.forEach(line => {
            if (line.Data < this.deep) line.destroy()
            maxDeep = Math.max(line.Data, maxDeep)
        })
        contentMonster.children.forEach(it => {
            if (it.Data.getLine() < this.deep) it.destroy()
        })
        for (let i = 0; i < list.length; i++) {
            let datas = list[i]
            let deepNow = datas[0]?.getLine()
            let line
            if (deepNow <= maxDeep) {
                line = content.children.find(l => l.Data == deepNow)
                let li = deepNow - this.deep
                line.x = li * line.width + content.Component(cc.Layout).spacingX * li
                contentMonster.children.forEach(m => {
                    if (m.Data.getLine() == deepNow) {
                        let node = line.children.find(e => e.Data.getRow() == m.Data.getRow())
                        m.setPosition(ut.convertToNodeAR(node, contentMonster))
                    }
                })
            }
            else {
                line = cc.instantiate2(this.lineNode_, content)
                let li = deepNow - this.deep
                line.x = li * line.width + content.Component(cc.Layout).spacingX * li
                line.Data = deepNow
                line.active = true
                for (let j = 0; j < datas.length; j++) {
                    let data = datas[j]
                    let it = cc.instantiate2(this.itemNode_, line)
                    it.active = true
                    this.updateLand(it, data)
                }
            }
            if (deepNow > this.deep + LINECAP) {
                line.active = false
            }
        }
    }

    //todo 这里有bug，当前操作节点setMapMask之前，点击下一页，deep变化，会让当前页面突然变黑，需要在设置的时候判断一下节点是否在视图范围内，如果不在就不更新了
    private setMapMask() {
        let content = this.mapViewNode_.Child('content')
        let contentMonster = this.mapViewNode_.Child('content_monster')
        content.children.forEach(l => {
            l.children.forEach(it => {

                //this.setOneMask(it, it.Data)
                this.setOOMask(it, it.Data)
            })
        })
        contentMonster.children.forEach(it => {
            let data: OreLandNode = it.Data
            let needMask = !this.hole.canReach(data.getRow(), data.getLine())
            it.setDark(needMask ? 0.5 : 0)
        })

    }

    private setOneMask(it: cc.Node, data: OreLandNode) {
        let mask = it.Child('mask')
        let maskNext = it.Child('maskNext')
        let maskSpecial = it.Child('maskSpecial')
        let canReach = this.hole.canReach(data.getRow(), data.getLine())
        mask.active = !canReach && data.getType() != OreLandType.NEXT && data.getType() != OreLandType.SPECIAL && data.getType() != OreLandType.BACK && data.getType() != OreLandType.NONE && data.getType() != OreLandType.START
        maskNext.active = !canReach && data.getType() == OreLandType.NEXT
        maskSpecial.active = !canReach && (data.getType() == OreLandType.SPECIAL || data.getType() == OreLandType.BACK)
    }

    private setOOMask(it: cc.Node, data: OreLandNode) {
        let canReach = this.hole.canReach(data.getRow(), data.getLine())
        let darkness = (!canReach && data.getType() != OreLandType.NONE && data.getType() != OreLandType.START) ? 0.3 : 0
        it.setDark(darkness, true)

    }

    private updateByPos(line: number, row: number) {
        let it = this.getNodeByPos(line, row)
        if (!it) return
        this.updateLand(it, this.hole.getOreNodeByPos(row, line))
    }

    private getNodeByPos(line: number, row: number) {
        let l = this.mapViewNode_.Child('content').children.find(e => e.Data == line)
        if (!l) return null
        let it = l.children.find(e => e.Data?.getRow() == row)
        if (!it) return null
        return it
    }

    private updateLand(it: cc.Node, data: OreLandNode) {
        if (!data) return   //这里有可能是敲了一个砖块（会播放动画，在动画结束后更新节点）后立即敲下一页（立即改变deep），更新的时候getOreNodeByPos()找到的data就为空
        it.Data = data
        it.y = -data.getRow() * it.height - it.anchorY * it.height - it.parent.Component(cc.Layout).spacingY * data.getRow()
        it.children.forEach(e => e.active = false)
        let monster = this.mapViewNode_.Child('content_monster').children.find(m => m.Data.getLine() == data.getLine() && m.Data.getRow() == data.getRow())
        if (!!monster) monster.destroy()
        it.off('click')
        it.on('click', () => {
            this.onClickLand(it, data)
        })
        if (data.isNone()) { return }
        it.Component(cc.Button).interactable = true
        let stone = it.Child('stone')
        let next = it.Child('next')
        let special = it.Child('special')
        let boom = it.Child('boom')
        let drill = it.Child('drill')
        let icon = it.Child('icon')
        let canReach = this.hole.canReach(data.getRow(), data.getLine())
        stone.active = data.getType() == OreLandType.GRAY || data.getType() == OreLandType.BLUE || data.isMonster() || data.getType() == OreLandType.BLACK || data.getType() == OreLandType.BREAK
        next.active = data.getType() == OreLandType.NEXT
        special.active = data.getType() == OreLandType.SPECIAL || data.getType() == OreLandType.BACK
        icon.active = data.hasReward()
        boom.active = data.getType() == OreLandType.BOOM
        drill.active = data.getType() == OreLandType.DRILL
        if (data.getType() == OreLandType.NONE) {
            icon.y = 0
        }
        else {
            icon.y = 34
        }
        this.setOOMask(it, data)
        if (stone.active) {
            if (data.getType() == OreLandType.BREAK) {
                stone.Component(sp.Skeleton).playAnimation(`jingzhi2_l`)
            }
            else if (data.getType() == OreLandType.BOSS || data.getType() == OreLandType.RUNAWAY_PURPLE) {
                stone.Component(sp.Skeleton).playAnimation('jingzhi3')
            }
            else {
                stone.Component(sp.Skeleton).playAnimation(`jingzhi${data.getType()}`)
            }
        }
        if (data.isMonster()) {
            this.setMonster(it, data, !canReach)
        }
        if (icon.active) {
            let reward = data.getRewards()[0]
            if (reward.type == ConditionType.ORE_ITEM) {
                resHelper.loadIcon(icon, 'ore/ore', `min_icon_${reward.id}${reward.num >= ManyNum ? '_2' : ''}`, this.getTag())
            }
        }
        if (next.active) {
            let nextType = data.getNextType()
            next.Component(cc.MultiFrame).setFrame(nextType)
            //maskNext.Component(cc.Mask).spriteFrame = next.Component(cc.MultiFrame).getFrame(nextType)
        }

        if (drill.active) {
            let dir = data.getDirection()
            if (dir == proto.OreBlockItemDrillDirection.Right) drill.angle = 0
            else if (dir == proto.OreBlockItemDrillDirection.Left) drill.angle = 180
            else if (dir == proto.OreBlockItemDrillDirection.Top) drill.angle = 90
            else if (dir == proto.OreBlockItemDrillDirection.Bottom) drill.angle = -90
        }
    }

    private setMonster(it: cc.Node, data: OreLandNode, needMask: boolean) {
        let monsterId = data.getMonsterId()
        if (!monsterId) return
        let monster = cc.instantiate2(this.monsterNode_, this.mapViewNode_.Child('content_monster'))
        monster.active = true
        monster.Data = data
        let sk = monster.Component(sp.Skeleton)
        let wait = 0.3
        let startTime = Date.now()
        resHelper.loadMonsterSp(monsterId, sk, this.getTag()).then(async () => {
            if (!cc.isValid(monster)) return
            monster.setDark(needMask ? 0.5 : 0)
            if (data.getType() == OreLandType.BOSS) {
                monster.active = false
                wait -= (Date.now() - startTime) / 1000
                if (wait > 0) {
                    await ut.wait(wait, this)
                }
                monster.active = true
                await sk.playAnimation(`${PassengerBattleAnimation.SKILL}_${1}_end`)
                resHelper.initMonsterSk(monsterId, sk)
            }
        })
        let scale = this.monsterNode_.scale
        if (cfgHelper.getCharacter(monsterId).isBoss && data.getType() != OreLandType.BOSS) {
            scale = 0.5
        }
        monster.scaleX = -scale
        monster.scaleY = scale
        monster.setPosition(ut.convertToNodeAR(it, this.mapViewNode_.Child('content_monster')))
    }

    private async moveRow(isUp: boolean = false, time?: number) {
        let row = ROWVIEW
        let content = this.mapViewNode_.Child('content')
        let contentMonster = this.mapViewNode_.Child('content_monster')
        let dis = (this.itemNode_.height + this.lineNode_.Component(cc.Layout).spacingY) * row * (isUp ? -1 : 1)
        time = time == void 0 ? MoveTime * row : time
        this.isMoving = true
        let p1 = cc.tween(content).to(time, { y: content.y + dis }).promise()
        let p2 = cc.tween(contentMonster).to(time, { y: contentMonster.y + dis }).promise()
        await Promise.all([p1, p2])
        //this.updateMap()
        this.isMoving = false
    }

    private async moveLine() {
        if (!this.isMoving && this.deep < this.hole.getDeep()) {
            await this.actLine(this.hole.getDeep() - this.deep)
        }
    }

    private async actLine(line: number) {
        let content = this.mapViewNode_.Child('content')
        let contentMonster = this.mapViewNode_.Child('content_monster')
        let hideNodes: cc.Node[] = []
        let originX = content.x
        let dis = (content.children[0].width + content.Component(cc.Layout).spacingX) * line
        let time = MoveTime * line
        content.children.forEach(li => {
            li.active = true
            /*
            if (li.Data < this.deep + line) {
                hideNodes.push(li)
            }
                */
        })
        this.isMoving = true
        let p1 = cc.tween(content).to(time, { x: originX - dis }).promise()
        let p2 = cc.tween(contentMonster).to(time, { x: originX - dis }).promise()
        await Promise.all([p1, p2])
        content.x = originX
        contentMonster.x = originX
        this.updateMap()
        this.isMoving = false
    }


    private updateDeep() {
        let deep = this.hole.getRealDeep() //gameHelper.ore.getRealDeep(this.level)
        let list = []
        for (let i = 0; i < DEEPCAP; i++) {
            if (deep > 0) list.push(deep % 10)
            else list.push(0)
            deep = Math.floor(deep / 10)
        }
        this.deepLineNode_.Child('ly').Items(list, (lb, num) => {
            lb.Component(cc.Label).string = `${num}`
        })
    }

    private bagChange(id: number) {
        let it = MagicCfg.find(e => e.propId == id)
        if (!!it) {
            this.updateMagic()
        }
    }

    private updateMagic() {
        this.magicNode_.children.forEach(it => {
            let num = gameHelper.bag.getPropCountById(MagicCfg.find(e => e.name == it.name).propId)
            // it.Child('select').active = it.name == String(this.curMagic)
            it.Child('select').active = false
            it.Child('count', cc.Label).string = `${num}${it.name == '1' ? `/${cfgHelper.getMiscData('ore').breakMaxNum}` : ''}`

            it.off('click')
            it.on('click', () => {
                this.curMagic = it.name
                this.updateMagic()
                if (it.name == '1' && this.adNode_.active) {
                    // viewHelper.showPnl("ore/OreWatchAdPnl")
                }
            })
        })
    }

    private async showBattle(data: OreLandNode) {
        let brothers = data.getRelation()
        let node = data
        //这里把操作节点转换成中间的boss节点
        if (!!brothers && brothers.length == 1) {
            node = this.hole.getOreNodeByPos(brothers[0].row, brothers[0].line)
        }
        let isBoss = node.getType() == OreLandType.BOSS
        let isRunAway = node.getType() == OreLandType.RUNAWAY_PURPLE

        let monsters = node.getMonsters()
        let rewards = node.getRewards().slice()
        if (isRunAway) {
            rewards = node.getHole().getLayerRewards()
        }
        let start = await new Promise(callback => {
            viewHelper.showPnl("battle/BattlePreview", {
                bg: BATTLE_PREVIEW_BG.MINE, title: { key: "name_chapter_blackHole" }, enemies: monsters, rewards, callback,
            })
        })
        if (!start) return

        let onWin = async (res) => {
            let extra
            if (isRunAway) {
                extra = res.allMonsters.filter(m => m.id == 2101).length - res.runAwayCnt
            }
            let data = await gameHelper.ore.operateLands(node, OreOperateType.BATTLE, this.level, extra)
            if (Array.isArray(data)) {
                rewards.length = 0
                rewards.pushArr(data)
            }
            if (data) {
                return true
            }
            return false
        }
        let desc = null, isWin
        let levelId = "0"
        if (isBoss) {
            levelId = "1"
        }
        if (isRunAway) {
            desc = assetsMgr.lang("ore_battle_desc_2")
            levelId = "2"
        }
        let noAgain = isRunAway
        let res = await viewHelper.showBattle({
            monsters,
            battleBg: resHelper.getBattleBg("mine"), isBlackHole: false,
            rewards, onWin,
            title: { name: 'blackHole_guiText_1', progress: '' },
            desc, isWin, noAgain,
            levelType: BattleLevelType.ORE, levelId,
        })
        if (res?.isWin) {
            return true
        }
        return false
    }

    //这里会结合表现刷新数据
    private async showEffects(opType: OreOperateType, it: cc.Node, data: OreLandNode) {
        //todo 这里结合opType和data.type来做处理
        if (!data) return
        let type = data.getType()
        let cond = data.getRewards().find(e => e.type == ConditionType.ORE_ITEM)
        if (!it) return
        if (opType == OreOperateType.ONE || opType == OreOperateType.TWICE) {
            if (type != OreLandType.NONE) {
                await this.showGaozi(opType, data, it)
            }
            else {
                await this.showStone(data, it)
            }
        }
        else if (opType == OreOperateType.BOOM || opType == OreOperateType.DRILL) {
            if (type == OreLandType.BOOM) {
                await this.showBoom(it, data)
            }
            else if (type == OreLandType.DRILL) {
                await this.showDrill(it, data)
            }
            else if (!CANTDAOJULIST.has(type)) {
                if (!data || !it) return
                if (type != OreLandType.NONE || !!cond) {
                    this.showSmoke(it)
                }
                await this.showStone(data, it, true)
            }
        }
        else {
        }
    }

    private async showGaozi(opType: OreOperateType, landData: OreLandNode, node: cc.Node) {
        let it = cc.instantiate2(this.daojuGroupNode_, this.daojuNode_)
        let gaozi = it.Child('daoju')
        let sk = it.Child('daoju', sp.Skeleton)
        let animName
        if (opType == OreOperateType.ONE) animName = 'gaozi1'
        if (opType == OreOperateType.TWICE) animName = 'gaozi2'
        gaozi.setPosition(ut.convertToNodeAR(node, this.daojuNode_))
        gaozi.active = true
        sk.playAnimation(animName)
        let time = sk.getEvent(animName)?.time
        await ut.wait(time, this)
        await this.showStone(landData, node)
        it.destroy()
    }

    private async showDrill(node: cc.Node, landData: OreLandNode) {
        let drillSpeed = 1500
        let waitTime = this.itemNode_.width / drillSpeed
        let it = cc.instantiate2(this.daojuGroupNode_, this.daojuNode_)
        let drill = it.Child('daoju')
        drill.active = true
        node.Child('drill').active = false
        let dir = landData.getDirection()
        if (dir == proto.OreBlockItemDrillDirection.Right) drill.angle = 90
        else if (dir == proto.OreBlockItemDrillDirection.Left) drill.angle = -90
        else if (dir == proto.OreBlockItemDrillDirection.Top) drill.angle = 180
        else if (dir == proto.OreBlockItemDrillDirection.Bottom) drill.angle = 0
        let sk = it.Child('daoju', sp.Skeleton)
        it.setPosition(ut.convertToNodeAR(node.Child('drill'), this.daojuNode_))
        await sk.playAnimation('zhuantou1')
        sk.playAnimation('zhuantou2', true)
        let nodes = landData.getDaojuNodes()
        //这里可能没有
        if (!nodes || nodes.length <= 0) {
            it.destroy()
            return
        }
        let lastNode = nodes[nodes.length - 1]
        let lastIt = this.getNodeByPos(lastNode.y, lastNode.x)
        let targetPos = ut.convertToNodeAR(lastIt.Child('drill'), it)
        cc.tween(drill).to(((nodes.length - 1) * this.itemNode_.width) / drillSpeed, { x: targetPos.x, y: targetPos.y }).call(() => {
            it.destroy()
        }).start()

        //钻头刚开始变大，表现层数据就更新
        this.operateByPos(landData.getLine(), landData.getRow())
        await ut.promiseMap(nodes, async (e) => {
            let step = Math.abs(e.x - landData.getRow()) + Math.abs(e.y - landData.getLine())
            if (step > 0) {//炸弹这个节点不操作
                await ut.wait(waitTime * step, this)
                let opNode = this.getNodeByPos(e.y, e.x)
                let opData = this.hole.getOreNodeByPos(e.x, e.y)
                await this.showEffects(OreOperateType.DRILL, opNode, opData)
            }
        })
    }

    private async showBoom(node: cc.Node, landData: OreLandNode) {
        let waitTime = 0.3
        let sk = node.Child('boom', sp.Skeleton)
        let anim = 'zhadan1'
        let nodes = landData.getDaojuNodes()
        sk.playAnimation(anim).then(() => {
            //炸弹刚爆开，表现层数据就更新
            this.operateByPos(landData.getLine(), landData.getRow())
        })
        let time = sk.getEvent(anim)?.time
        await ut.wait(time, this)
        //这里需要算炸弹周围的节点，分两次处理，离得近的先炸，远一点的后炸
        await ut.promiseMap(nodes, async (e) => {
            let step = Math.abs(e.x - landData.getRow()) + Math.abs(e.y - landData.getLine())
            if (step > 0) {//炸弹这个节点不操作
                await ut.wait(waitTime * (step - 1), this)
                let opNode = this.getNodeByPos(e.y, e.x)
                let opData = this.hole.getOreNodeByPos(e.x, e.y)
                await this.showEffects(OreOperateType.BOOM, opNode, opData)
            }
        })
    }

    private async showSmoke(node: cc.Node) {
        let it = cc.instantiate2(this.smokeGroupNode_, this.smokeNode_)
        let smoke = it.Child('smoke')
        smoke.setPosition(ut.convertToNodeAR(node, this.daojuNode_))
        smoke.active = true
        let sk = it.Child('smoke', sp.Skeleton)
        sk.scheduleUpdate(() => {
            smoke.setPosition(ut.convertToNodeAR(node, this.daojuNode_))
        })
        let rand = ut.getRandomNum(1, 3, false)
        sk.playAnimation(`animation${rand}`).then(() => {
            it.destroy()
        })
    }

    private async showStone(landData: OreLandNode, node: cc.Node, isBombed: boolean = false) {
        let type = landData.getType()
        let cond = landData.getRewards()[0]
        let anim = null
        let time = 0
        if (type == OreLandType.GRAY) anim = 'animation1'
        else if (type == OreLandType.BLUE) anim = 'animation2'
        else if (type == OreLandType.BREAK) anim = 'animation2_l'
        else if (type == OreLandType.BLACK) anim = 'animation4_l'
        if (anim) {
            let stone = node.Child('stone')
            let skStone = node.Child('stone', sp.Skeleton)
            skStone.playAnimation(anim, false)
            time = skStone.getAnimationDuration(anim)
            stone.active = !isBombed
        }
        let skin = null
        if (type == OreLandType.GRAY) skin = 'kuang2'
        else if (type == OreLandType.BLUE || type == OreLandType.BREAK) skin = 'kuang1'
        else if (type == OreLandType.BLACK) skin = 'kuang3'
        if (skin) {
            let it = cc.instantiate2(this.rockGroupNode_, this.rockNode_)
            let rock = it.Child('rock')
            let skRock = it.Child('rock', sp.Skeleton)
            skRock.scheduleUpdate(() => {
                rock.setPosition(ut.convertToNodeAR(node, this.rockNode_))
            })
            rock.setPosition(ut.convertToNodeAR(node, this.rockNode_))
            rock.active = true
            skRock.setSkin(skin)
            let rand = ut.getRandomNum(1, 3, false)
            skRock.playAnimation(`animation${rand}`).then(() => {
                it.destroy()
            })
        }

        if (!!cond && (type == OreLandType.GRAY || type == OreLandType.BREAK || (type == OreLandType.BLUE && isBombed) || type == OreLandType.NONE)) this.showOre(cond, node)

        //这里是在每个格子表现结束的时候同步表现层的数据
        this._mustUpdateNow = () => this.operateByPos(landData.getLine(), landData.getRow())
        await ut.wait(time, this)
        this._mustUpdateNow = null
        this.operateByPos(landData.getLine(), landData.getRow())
    }

    private async showOre(cond: ConditionObj, it: cc.Node) {
        let group = cc.instantiate2(this.rewardGroupNode_, this.rewardNode_)
        let num = Math.min(cond.num, 10)
        group.active = true
        group.setPosition(ut.convertToNodeAR(it.Child('icon'), this.rewardNode_))
        it.Child('icon').active = false
        let list = DiamondPos.slice()
        list = ut.randomArray(list)
        list.splice(num - 1, DiamondPos.length - num)
        group.Items(list, (it, data) => {
            resHelper.loadIconByCondInfo(cond, it.Child('val'), this.getTag())
            this.floatOut(it, data)
        })

        await ut.wait(FloatTime, this)
        this.rewardNode_.children.forEach((group) => {
            group.children.forEach((it) => {
                this.float(it)
            })
        })
        await ut.wait(FloatStopTime, this)
        let timeMin = 1000, timeMax = 0
        this.rewardNode_.children.forEach((group) => {
            group.children.forEach((it) => {
                //it.children.forEach((cond) => { cond.active && animHelper.playTrailingParticle(cond) })
                animHelper.playTrailingParticle(it)
                let time = this.fly(it)
                timeMax = Math.max(timeMax, time)
                timeMin = Math.min(timeMin, time)
            })
        })
        await ut.wait(timeMin, this)
        animHelper.scaleBag(this.makeNode_)
    }

    //FloatTime以内飘出，pos和size变化
    private async floatOut(it: cc.Node, pos: { x, y }) {
        let bornTime = ut.getRandomNum(0, FloatBornTimeMax, true, 3)
        let scale = ut.getRandomNum(FloatScaleMin, FloatScaleMax, true, 2)
        await ut.wait(bornTime, this)
        cc.tween(it).to(FloatTime - bornTime, { x: pos.x / 2, y: pos.y / 2, scale }, { easing: cc.easing.circOut }).start()
    }

    private async float(it: cc.Node) {
        let targetX = it.x * 1.2
        let targetY = it.y * 1.2
        cc.tween(it).to(FloatStopTime, { x: targetX, y: targetY }, { easing: cc.easing.sineIn }).start()
    }

    //FlyTime以内飘到终点
    private fly(it: cc.Node) {
        let target = this.makeNode_
        let targetPos = ut.convertToNodeAR(target, it.parent)
        let vec = targetPos.sub(it.getPosition())
        let time = Math.min(vec.len() / FlySpeed, FlyTime)
        cc.tween(it).to(time, { x: targetPos.x, y: targetPos.y, scale: EndScale, angle: 0 }, { easing: cc.easing.sineOut })
            .call(() => {
                it.destroy()
            }).start()
        return time
    }


    /*
    //todo 这里daojuList直接存节点的showData数据
    private getShowData(data: OreLandNode) {
        let res: showData = { x: data.getRow(), y: data.getLine(), type: data.getType(), cond: data.getRewards(), daojuList: data.getDaojuNodes() }
        return res
    }
        */

    private async playSceneChange(it: cc.Node, type: OreLandType) {
        if (!cc.isValid(this.specialNode_)) {
            return
        }
        const spin = this.specialNode_.Component(sp.Skeleton)
        this.isMoving = true
        spin.node.active = true
        const time = spin.getAnimationDuration(anim)
        spin.playAnimation(anim, false).then(() => {
            //spin.node.active = false
        })

        ut.wait(spin.getEvent(anim, "BG2").time, this).then(async () => {
            await this.moveRow(type == OreLandType.BACK, 0)
            this.setMapMask()
        })

        await ut.wait(time, this)

        this.isMoving = false
    }

}

const anim = "animation"