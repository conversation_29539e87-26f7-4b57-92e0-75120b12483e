/**
 * 车厢施工计划优化器 - 支持小数日期版本
 * 
 * 混合架构设计：
 * - 内部使用数组结构进行高效优化
 * - 接口层支持小数日期输入输出
 * - 保持完整的"削峰填谷"优化算法
 * 
 * <AUTHOR> Assistant
 * @version 2.0 - 支持小数日期
 */

/**
 * @typedef {object} Carriage
 * @property {string | number} id - 车厢的唯一标识符
 * @property {number} B - 需要修建的设施总数
 * @property {number} S - 开始修建日期 (天, 从1开始，支持小数，如1.5)
 * @property {number} E - 结束修建日期 (天, 从1开始，支持小数，如10.2)
 * @property {number} [firstDayAmount] - 第一天的修建数量的最小值，实际值会大于等于这个参数 (可选)
 */

/**
 * @typedef {object} ScheduleOptions
 * @property {number} [iterations=30000] - 优化的迭代次数，次数越多，结果越平滑
 */

/**
 * 车厢小数日期信息
 * @typedef {object} CarriageDecimalInfo
 * @property {number} startDay - 整数开始天 (1-based)
 * @property {number} endDay - 整数结束天 (1-based)
 * @property {number} startRatio - 开始天的工作比例 (0-1)
 * @property {number} endRatio - 结束天的工作比例 (0-1)
 * @property {Array<{day: number, ratio: number}>} workDays - 所有工作天及其比例
 */

/**
 * 使用"削峰填谷"算法为车厢建设计划生成每日施工方案，以实现负载均衡。
 * 支持每个车厢每天修建设施数递增的约束条件，以及小数日期。
 *
 * @param {Carriage[]} carriages - 一个包含所有车厢信息的数组。
 * @param {ScheduleOptions} [options] - 可选的配置项。
 * @returns {{finalPlan: Object.<string, Object.<string, number>>, totalDailyLoad: number[], feasible: boolean, score: number}} - 返回一个对象，包含每个车厢的最终每日计划、整个项目的每日总负载和可行性标志。
 */
function scheduleConstruction(carriages, options = {}) {
    const {
        iterations = 100000 // 适中的迭代次数，给随机扰动更多机会
    } = options;

    if (!carriages || carriages.length === 0) {
        return {
            finalPlan: {},
            totalDailyLoad: [],
            feasible: true,
            score: 0
        };
    }

    // --- 预处理：解析小数日期信息 ---
    const carriageInfos = carriages.map(carriage => ({
        ...carriage,
        decimalInfo: parseDecimalDates(carriage.S, carriage.E)
    }));

    // --- 步骤 1: 可行性检查 ---
    for (const carriage of carriageInfos) {
        if (!isCarriageFeasible(carriage)) {
            console.warn(`车厢 ${carriage.id} 无法在给定约束下完成任务`);
            return {
                finalPlan: {},
                totalDailyLoad: [],
                feasible: false,
                score: 0
            };
        }
    }

    // --- 步骤 2: 创建内部数组表示用于优化 ---
    const maxDay = Math.max(...carriageInfos.map(c => c.decimalInfo.endDay));
    let totalDailyLoad = new Array(maxDay).fill(0);
    let carriagePlans = {}; // 使用对象存储每个车厢的浮点数计划

    // 为每个车厢创建满足小波动约束的初始分配
    for (const carriage of carriageInfos) {
        const plan = generateMonotonicPlan(carriage, maxDay);
        carriagePlans[carriage.id] = plan;
        
        // 更新总负载
        for (let day = 0; day < maxDay; day++) {
            totalDailyLoad[day] += plan[day];
        }
    }
    
    // 预处理：识别并减轻车厢结束导致的大跳跃
    for (const carriage of carriageInfos) {
        const endDay = carriage.decimalInfo.endDay - 1; // 转换为0-based
        if (endDay < maxDay - 1) {
            // 检查车厢结束日和下一天的负载差异
            const jumpSize = totalDailyLoad[endDay] - totalDailyLoad[endDay + 1];
            if (jumpSize > 20) {
                // 尝试将部分负载从结束日前的几天转移到后面
                const plan = carriagePlans[carriage.id];
                const startDay = carriage.decimalInfo.startDay - 1; // 转换为0-based
                
                // 从倒数第二天向前几天减少一些负载
                for (let day = endDay - 1; day >= startDay && jumpSize > 15; day--) {
                    const reduceAmount = Math.min(plan[day] * 0.1, 3);
                    if (plan[day] - reduceAmount > (day > startDay ? plan[day - 1] + 1 : 2)) {
                        plan[day] -= reduceAmount;
                        plan[endDay] += reduceAmount;
                        totalDailyLoad[day] -= reduceAmount;
                        totalDailyLoad[endDay] += reduceAmount;
                    }
                }
            }
        }
    }

    // --- 步骤 3: 约束保持的迭代优化 ("削峰填谷") ---
    for (let i = 0; i < iterations; i++) {
        let maxLoad = -1, minLoad = Infinity;
        let maxLoadDay = -1, minLoadDay = -1;

        // 找到负载最高和最低的两天
        for (let day = 0; day < maxDay; day++) {
            if (totalDailyLoad[day] > maxLoad) {
                maxLoad = totalDailyLoad[day];
                maxLoadDay = day;
            }
            if (totalDailyLoad[day] < minLoad) {
                minLoad = totalDailyLoad[day];
                minLoadDay = day;
            }
        }

        // 如果负载差异很小，添加随机扰动或结束
        if (maxLoad - minLoad < 1) {
            // 每1000次迭代有机会进行随机扰动，防止局部最优
            if (i % 1000 === 0 && i < iterations * 0.8) {
                // 进行随机扰动
                performRandomPerturbation(carriageInfos, carriagePlans, totalDailyLoad, maxDay);
                continue;
            } else {
                break;
            }
        }

        // 尝试多种负载转移策略
        let transferSuccess = false;
        
        // 策略1: 寻找能同时覆盖高峰和低谷日的车厢
        // 添加随机性：随机打乱车厢顺序
        const shuffledCarriages = [...carriageInfos].sort(() => Math.random() - 0.5);
        
        for (const carriage of shuffledCarriages) {
            const startIndex = carriage.decimalInfo.startDay - 1; // 转换为0-based
            const endIndex = carriage.decimalInfo.endDay - 1; // 转换为0-based

            if (startIndex <= minLoadDay && endIndex >= minLoadDay && 
                startIndex <= maxLoadDay && endIndex >= maxLoadDay) {
                
                const success = transferWithMonotonicConstraint(
                    carriage, carriagePlans, totalDailyLoad, 
                    maxLoadDay, minLoadDay
                );
                
                if (success) {
                    transferSuccess = true;
                    break;
                }
            }
        }
        
        // 策略2: 优先处理大跳跃，特别是相邻日期的大幅变化
        if (!transferSuccess) {
            // 首先找出最大的相邻日期跳跃
            let maxJump = 0;
            let jumpFromDay = -1, jumpToDay = -1;
            
            for (let day = 0; day < maxDay - 1; day++) {
                const jump = Math.abs(totalDailyLoad[day] - totalDailyLoad[day + 1]);
                if (jump > maxJump) {
                    maxJump = jump;
                    jumpFromDay = totalDailyLoad[day] > totalDailyLoad[day + 1] ? day : day + 1;
                    jumpToDay = totalDailyLoad[day] > totalDailyLoad[day + 1] ? day + 1 : day;
                }
            }
            
            // 如果找到大跳跃（超过20），优先处理
            if (maxJump > 20) {
                for (const carriage of carriageInfos) {
                    const startIndex = carriage.decimalInfo.startDay - 1;
                    const endIndex = carriage.decimalInfo.endDay - 1;
                    
                    if (startIndex <= jumpToDay && endIndex >= jumpFromDay) {
                        const success = transferWithMonotonicConstraint(
                            carriage, carriagePlans, totalDailyLoad, 
                            jumpFromDay, jumpToDay
                        );
                        
                        if (success) {
                            transferSuccess = true;
                            break;
                        }
                    }
                }
            }
            
            // 如果没有大跳跃或处理失败，则按原来的逻辑处理较小的波动
            if (!transferSuccess) {
                for (let day = 0; day < maxDay - 1; day++) {
                    if (Math.abs(totalDailyLoad[day] - totalDailyLoad[day + 1]) > 2) {
                        for (const carriage of carriageInfos) {
                            const startIndex = carriage.decimalInfo.startDay - 1;
                            const endIndex = carriage.decimalInfo.endDay - 1;
                            
                            if (startIndex <= day && endIndex >= day + 1) {
                                const fromDay = totalDailyLoad[day] > totalDailyLoad[day + 1] ? day : day + 1;
                                const toDay = totalDailyLoad[day] > totalDailyLoad[day + 1] ? day + 1 : day;
                                
                                const success = transferWithMonotonicConstraint(
                                    carriage, carriagePlans, totalDailyLoad, 
                                    fromDay, toDay
                                );
                                
                                if (success) {
                                    transferSuccess = true;
                                    break;
                                }
                            }
                        }
                        if (transferSuccess) break;
                    }
                }
            }
        }
        
        // 策略3: 尝试多天范围内的负载平滑
        if (!transferSuccess) {
            for (let span = 2; span <= 4 && !transferSuccess; span++) { // 2-4天的范围
                for (let day = 0; day < maxDay - span; day++) {
                    let maxInSpan = -1, minInSpan = Infinity;
                    let maxDayInSpan = -1, minDayInSpan = -1;
                    
                    // 找到范围内的最高和最低负载
                    for (let d = day; d < day + span; d++) {
                        if (totalDailyLoad[d] > maxInSpan) {
                            maxInSpan = totalDailyLoad[d];
                            maxDayInSpan = d;
                        }
                        if (totalDailyLoad[d] < minInSpan) {
                            minInSpan = totalDailyLoad[d];
                            minDayInSpan = d;
                        }
                    }
                    
                    if (maxInSpan - minInSpan > 3) { // 如果范围内波动较大
                        for (const carriage of carriageInfos) {
                            const startIndex = carriage.decimalInfo.startDay - 1;
                            const endIndex = carriage.decimalInfo.endDay - 1;
                            
                            if (startIndex <= minDayInSpan && endIndex >= maxDayInSpan) {
                                const success = transferWithMonotonicConstraint(
                                    carriage, carriagePlans, totalDailyLoad, 
                                    maxDayInSpan, minDayInSpan
                                );
                                
                                if (success) {
                                    transferSuccess = true;
                                    break;
                                }
                            }
                        }
                        if (transferSuccess) break;
                    }
                }
            }
        }
        
        // 如果都没有成功转移，说明已经收敛
        if (!transferSuccess) {
            break;
        }
    }

    // --- 步骤 4: 将数组结果转换为小数日期格式 ---
    const finalPlan = {};
    for (const carriage of carriageInfos) {
        finalPlan[carriage.id] = convertArrayToDecimalPlan(
            carriage, 
            finalizeCarriagePlan(carriage, carriagePlans[carriage.id], maxDay)
        );
    }
    
    // --- 步骤 5: 重新计算最终总负载（基于小数日期格式） ---
    const finalTotalDailyLoad = new Array(maxDay).fill(0);
    for (const carriage of carriageInfos) {
        const plan = finalPlan[carriage.id];
        for (const dayStr in plan) {
            const dayFloat = parseFloat(dayStr);
            const dayInt = Math.floor(dayFloat) - 1; // 转换为0-based索引
            if (dayInt >= 0 && dayInt < maxDay) {
                finalTotalDailyLoad[dayInt] += plan[dayStr];
            }
        }
    }

    // 计算方案评分
    const score = calculateSolutionScore(finalTotalDailyLoad);
    
    return {
        finalPlan,
        totalDailyLoad: finalTotalDailyLoad,
        feasible: true,
        score: score
    };
}

/**
 * 解析小数日期信息
 * @param {number} S - 开始日期（可能是小数）
 * @param {number} E - 结束日期（可能是小数）
 * @returns {CarriageDecimalInfo}
 */
function parseDecimalDates(S, E) {
    const startDay = Math.floor(S);
    const endDay = Math.ceil(E);
    const startRatio = startDay === S ? 1 : (1 - (S - startDay));
    const endRatio = endDay === E ? 1 : (E - Math.floor(E));
    
    const workDays = [];
    
    // 开始天
    if (startDay <= endDay) {
        workDays.push({
            day: startDay,
            ratio: startDay === endDay ? (endRatio - (1 - startRatio)) : startRatio
        });
    }
    
    // 中间完整天
    for (let day = startDay + 1; day < endDay; day++) {
        workDays.push({ day, ratio: 1 });
    }
    
    // 结束天（如果不同于开始天）
    if (endDay > startDay && endRatio > 0) {
        workDays.push({ day: endDay, ratio: endRatio });
    }
    
    return {
        startDay,
        endDay,
        startRatio,
        endRatio,
        workDays
    };
}

/**
 * 将数组格式的计划转换为小数日期格式
 * @param {object} carriage - 车厢信息（包含decimalInfo）
 * @param {number[]} arrayPlan - 数组格式的计划
 * @returns {Object.<string, number>} - 小数日期格式的计划
 */
function convertArrayToDecimalPlan(carriage, arrayPlan) {
    const { decimalInfo } = carriage;
    const decimalPlan = {};
    
    for (const workDay of decimalInfo.workDays) {
        const arrayIndex = workDay.day - 1; // 转换为0-based
        if (arrayIndex >= 0 && arrayIndex < arrayPlan.length) {
            const dailyAmount = arrayPlan[arrayIndex];
            const actualAmount = Math.round(dailyAmount * workDay.ratio);
            
            if (actualAmount > 0) {
                if (workDay.ratio === 1) {
                    // 完整天
                    decimalPlan[workDay.day] = actualAmount;
                } else {
                    // 小数天
                    const decimalDay = workDay.day === decimalInfo.startDay ? carriage.S : carriage.E;
                    decimalPlan[decimalDay] = actualAmount;
                }
            }
        }
    }
    
    return decimalPlan;
}

/**
 * 检查单个车厢是否在小波动约束下可行
 * @param {object} carriage - 车厢信息（包含decimalInfo）
 * @returns {boolean}
 */
function isCarriageFeasible(carriage) {
    const { B, firstDayAmount, decimalInfo } = carriage;
    const duration = decimalInfo.workDays.length;
    
    if (duration <= 0 || B <= 0) return false;
    
    // 确定第一天的修建数量最小值
    const minFirstDay = Math.max(firstDayAmount || 2, 2);
    
    // 递增约束下的最小可能总量
    // 最小情况：每天都等于前一天，总量为 duration * minFirstDay
    const minPossibleTotal = duration * minFirstDay;
    
    return B >= minPossibleTotal;
}

/**
 * 为单个车厢生成满足递增约束的初始计划
 * @param {object} carriage - 车厢信息（包含decimalInfo）
 * @param {number} maxDay 
 * @returns {number[]}
 */
function generateMonotonicPlan(carriage, maxDay) {
    const { id, B, firstDayAmount, decimalInfo } = carriage;
    const plan = new Array(maxDay).fill(0);
    const startIndex = decimalInfo.startDay - 1; // 转换为0-based
    const endIndex = decimalInfo.endDay - 1; // 转换为0-based
    const duration = endIndex - startIndex + 1;

    if (duration <= 0) return plan;

    // 确定第一天的修建数量最小值
    const minFirstDay = Math.max(firstDayAmount || 2, 2);

    // 使用等差数列分配，确保满足递增约束 (每天 >= 前一天)
    let a = minFirstDay; // 第一天数量
    let d = 0; // 公差

    // 计算在约束下能分配的最小总量
    let minTotal = duration * a; // 全部为第一天数量的总量
    let remaining = B - minTotal;

    if (remaining > 0 && duration > 1) {
        // 计算等差数列公差，确保递增
        d = remaining * 2 / (duration * (duration - 1)); // 等差数列公差
        
        // 确保公差为非负数（递增）
        d = Math.max(d, 0);
    }

    // 分配每天的数量
    for (let i = 0; i < duration; i++) {
        plan[startIndex + i] = a + i * d;
    }

    // 校正总量误差
    let currentTotal = 0;
    for (let i = startIndex; i <= endIndex; i++) {
        currentTotal += plan[i];
    }

    let diff = B - currentTotal;
    if (Math.abs(diff) > 1e-6) {
        // 按比例调整，优先调整后面的天数
        for (let i = endIndex; i >= startIndex && Math.abs(diff) > 1e-6; i--) {
            const adjustment = diff / (i - startIndex + 1);
            plan[i] += adjustment;
            diff -= adjustment;
        }
    }

    return plan;
}

/**
 * 在保持递增约束的前提下进行负载转移
 * @param {object} carriage - 车厢信息（包含decimalInfo）
 * @param {Object} carriagePlans 
 * @param {number[]} totalDailyLoad 
 * @param {number} fromDay 
 * @param {number} toDay 
 * @returns {boolean} 是否成功转移
 */
function transferWithMonotonicConstraint(carriage, carriagePlans, totalDailyLoad, fromDay, toDay) {
    const plan = carriagePlans[carriage.id];
    const startIndex = carriage.decimalInfo.startDay - 1; // 转换为0-based
    const endIndex = carriage.decimalInfo.endDay - 1; // 转换为0-based
    
    // 计算安全的转移量
    const loadDiff = totalDailyLoad[fromDay] - totalDailyLoad[toDay];
    const baseTransfer = Math.min(
        plan[fromDay] * 0.2, // 增加到20%的转移比例
        loadDiff / 3, // 更积极的转移量
        5 // 每次最多转移5个单位，避免过度调整
    );
    
    // 如果是大跳跃（差异超过30），使用更积极的转移策略
    const isBigJump = Math.abs(totalDailyLoad[fromDay] - totalDailyLoad[toDay]) > 30;
    
    let maxTransfer;
    if (isBigJump) {
        // 对于大跳跃，使用更积极的转移
        maxTransfer = Math.min(
            plan[fromDay] * 0.4, // 对大跳跃使用40%的转移比例
            loadDiff / 2, // 更大的转移量
            10 // 允许更大的单次转移
        );
    } else {
        // 添加随机性：在基础转移量的50%-150%之间随机选择
        const randomFactor = 0.5 + Math.random(); // 0.5 到 1.5
        maxTransfer = baseTransfer * randomFactor;
    }
    
    if (maxTransfer <= 1e-9) return false;
    
    // 模拟转移，检查是否会违反递增约束
    const testPlan = [...plan];
    testPlan[fromDay] -= maxTransfer;
    testPlan[toDay] += maxTransfer;
    
    // 检查递增约束（每天 >= 前一天）
    for (let day = startIndex; day < endIndex; day++) {
        if (testPlan[day + 1] < testPlan[day] - 1e-9) { // 下一天不能小于前一天
            return false; // 违反递增约束
        }
    }
    
    // 如果约束检查通过，执行实际转移
    plan[fromDay] -= maxTransfer;
    plan[toDay] += maxTransfer;
    totalDailyLoad[fromDay] -= maxTransfer;
    totalDailyLoad[toDay] += maxTransfer;
    
    return true;
}

/**
 * 计算方案的综合评分，分数越高越好
 * @param {number[]} totalDailyLoad 
 * @returns {number}
 */
function calculateSolutionScore(totalDailyLoad) {
    if (!totalDailyLoad || totalDailyLoad.length === 0) return 0;
    
    let score = 1000; // 基础分
    
    // 1. 负载方差惩罚 (权重: 高)
    const validLoads = totalDailyLoad.filter(load => load > 0);
    if (validLoads.length > 1) {
        const mean = validLoads.reduce((sum, load) => sum + load, 0) / validLoads.length;
        const variance = validLoads.reduce((sum, load) => sum + Math.pow(load - mean, 2), 0) / validLoads.length;
        score -= variance * 0.1; // 方差越大，扣分越多
    }
    
    // 2. 相邻跳跃惩罚 (权重: 很高)
    let totalJumpPenalty = 0;
    for (let i = 0; i < totalDailyLoad.length - 1; i++) {
        if (totalDailyLoad[i] > 0 && totalDailyLoad[i + 1] > 0) {
            const jump = Math.abs(totalDailyLoad[i + 1] - totalDailyLoad[i]);
            if (jump > 20) {
                totalJumpPenalty += Math.pow(jump - 20, 1.5); // 大跳跃指数惩罚
            } else if (jump > 10) {
                totalJumpPenalty += (jump - 10) * 2; // 中等跳跃线性惩罚
            }
        }
    }
    score -= totalJumpPenalty;
    
    // 3. 平稳性奖励 (权重: 中等)
    let stabilityBonus = 0;
    let consecutiveStableCount = 0;
    for (let i = 1; i < totalDailyLoad.length; i++) {
        if (totalDailyLoad[i] > 0 && totalDailyLoad[i - 1] > 0) {
            const change = Math.abs(totalDailyLoad[i] - totalDailyLoad[i - 1]);
            if (change <= 5) {
                consecutiveStableCount++;
                stabilityBonus += 2; // 平稳区间奖励
            } else {
                consecutiveStableCount = 0;
            }
            
            // 连续平稳奖励
            if (consecutiveStableCount >= 3) {
                stabilityBonus += consecutiveStableCount * 0.5;
            }
        }
    }
    score += stabilityBonus;
    
    // 4. 平缓下降奖励 (权重: 中等)
    let gradualDeclineBonus = 0;
    for (let i = 1; i < totalDailyLoad.length; i++) {
        if (totalDailyLoad[i] > 0 && totalDailyLoad[i - 1] > 0) {
            const decline = totalDailyLoad[i - 1] - totalDailyLoad[i];
            if (decline > 0 && decline <= 15) {
                gradualDeclineBonus += 3; // 平缓下降奖励
            }
        }
    }
    score += gradualDeclineBonus;
    
    // 5. 极值惩罚 (权重: 中等)
    const maxLoad = Math.max(...validLoads);
    const minLoad = Math.min(...validLoads);
    if (maxLoad - minLoad > 80) {
        score -= (maxLoad - minLoad - 80) * 2; // 极差过大惩罚
    }
    
    return Math.round(score * 100) / 100; // 保留两位小数
}

/**
 * 执行随机扰动以帮助跳出局部最优
 * @param {object[]} carriages - 车厢信息数组（包含decimalInfo）
 * @param {Object} carriagePlans 
 * @param {number[]} totalDailyLoad 
 * @param {number} maxDay 
 */
function performRandomPerturbation(carriages, carriagePlans, totalDailyLoad, maxDay) {
    // 随机选择一个车厢进行扰动
    const randomCarriage = carriages[Math.floor(Math.random() * carriages.length)];
    const startIndex = randomCarriage.decimalInfo.startDay - 1;
    const endIndex = randomCarriage.decimalInfo.endDay - 1;
    const duration = endIndex - startIndex + 1;
    
    if (duration < 2) return; // 工期太短，无法扰动
    
    // 随机选择两个不同的工作日
    const day1 = startIndex + Math.floor(Math.random() * duration);
    let day2 = startIndex + Math.floor(Math.random() * duration);
    while (day2 === day1 && duration > 1) {
        day2 = startIndex + Math.floor(Math.random() * duration);
    }
    
    // 确保day1 < day2
    const fromDay = Math.max(day1, day2);
    const toDay = Math.min(day1, day2);
    
    // 进行小幅度的随机转移
    const perturbationAmount = Math.random() * 2; // 0-2的随机扰动量
    
    // 尝试转移（如果违反约束则回滚）
    const plan = carriagePlans[randomCarriage.id];
    const originalFrom = plan[fromDay];
    const originalTo = plan[toDay];
    
    plan[fromDay] -= perturbationAmount;
    plan[toDay] += perturbationAmount;
    
    // 检查是否违反递增约束
    let violatesConstraint = false;
    for (let day = startIndex; day < endIndex; day++) {
        if (plan[day + 1] < plan[day] - 1e-9) { // 下一天不能小于前一天
            violatesConstraint = true;
            break;
        }
    }
    
    if (violatesConstraint || plan[fromDay] < 2) {
        // 回滚
        plan[fromDay] = originalFrom;
        plan[toDay] = originalTo;
    } else {
        // 更新总负载
        totalDailyLoad[fromDay] -= perturbationAmount;
        totalDailyLoad[toDay] += perturbationAmount;
    }
}

/**
 * 对车厢计划进行最终化处理（取整并保证总量和递增约束）
 * @param {object} carriage - 车厢信息（包含decimalInfo）
 * @param {number[]} plan 
 * @param {number} maxDay 
 * @returns {number[]}
 */
function finalizeCarriagePlan(carriage, plan, maxDay) {
    const { id, B, decimalInfo } = carriage;
    const finalPlan = new Array(maxDay).fill(0);
    const startIndex = decimalInfo.startDay - 1;
    const endIndex = decimalInfo.endDay - 1;
    
    // 第一步：取整
    let currentTotal = 0;
    const workDays = [];
    
    for (let day = startIndex; day <= endIndex; day++) {
        const integerPart = Math.floor(plan[day]);
        finalPlan[day] = Math.max(2, integerPart); // 确保至少为2
        currentTotal += finalPlan[day];
        workDays.push({
            day,
            fractionalPart: plan[day] - integerPart
        });
    }
    
    // 第二步：分配余量
    let remainder = B - currentTotal;
    workDays.sort((a, b) => b.fractionalPart - a.fractionalPart);
    
    // 优先给小数部分大的天分配余量，但要保持递增
    for (let i = 0; i < Math.min(remainder, workDays.length); i++) {
        finalPlan[workDays[i].day]++;
    }
    remainder -= Math.min(remainder, workDays.length);
    
    // 第三步：确保递增约束和总量约束
    ensureMonotonicAndTotal(finalPlan, startIndex, endIndex, B);
    
    return finalPlan;
}

/**
 * 确保计划满足递增约束和总量约束
 * @param {number[]} plan 
 * @param {number} startIndex 
 * @param {number} endIndex 
 * @param {number} targetTotal 
 */
function ensureMonotonicAndTotal(plan, startIndex, endIndex, targetTotal) {
    // 第一轮：确保递增约束
    for (let day = startIndex + 1; day <= endIndex; day++) {
        if (plan[day] < plan[day - 1]) {
            // 如果当天小于前一天，调整为等于前一天
            plan[day] = plan[day - 1];
        }
    }
    
    // 第二轮：调整总量
    let currentTotal = 0;
    for (let day = startIndex; day <= endIndex; day++) {
        currentTotal += plan[day];
    }
    
    let diff = targetTotal - currentTotal;
    
    // 如果总量不足，从后往前增加
    while (diff > 0) {
        for (let day = endIndex; day >= startIndex && diff > 0; day--) {
            plan[day]++;
            diff--;
        }
    }
    
    // 如果总量过多，尝试从前往后减少（但要保持递增）
    while (diff < 0) {
        let reduced = false;
        for (let day = startIndex; day <= endIndex && diff < 0; day++) {
            // 检查是否可以减少而不违反递增约束
            const prevDayValue = (day > startIndex) ? plan[day - 1] : 2;
            const nextDayValue = (day < endIndex) ? plan[day + 1] : plan[day];
            
            // 当前天至少要等于前一天，且减少后下一天仍能保持递增
            const minAllowed = Math.max(2, prevDayValue);
            if (plan[day] > minAllowed && (day === endIndex || nextDayValue >= plan[day] - 1)) {
                plan[day]--;
                diff++;
                reduced = true;
            }
        }
        if (!reduced) break; // 无法进一步减少
    }
}

/**
 * 运行多次优化并找到最佳方案
 * @param {Carriage[]} carriages 
 * @param {number} runs 
 * @returns {Object}
 */
function findBestSolution(carriages, runs = 5) {
    console.log(`🔍 运行 ${runs} 次优化，寻找最佳方案...\n`);
    
    let bestSolution = null;
    let allScores = [];
    
    for (let i = 0; i < runs; i++) {
        const result = scheduleConstruction(carriages);
        allScores.push(result.score);
        
        // console.log(`第 ${i + 1} 次运行 - 评分: ${result.score}`);
        
        if (!bestSolution || result.score > bestSolution.score) {
            bestSolution = result;
            console.log(`  🎉 新的最佳方案！`);
        }
    }
    
    console.log(`\n📈 评分统计:`);
    console.log(`  最高分: ${Math.max(...allScores)}`);
    console.log(`  最低分: ${Math.min(...allScores)}`);
    console.log(`  平均分: ${(allScores.reduce((a, b) => a + b, 0) / allScores.length).toFixed(2)}`);
    console.log(`  标准差: ${Math.sqrt(allScores.reduce((sum, score) => sum + Math.pow(score - allScores.reduce((a, b) => a + b, 0) / allScores.length, 2), 0) / allScores.length).toFixed(2)}`);
    
    return bestSolution;
}

// --- 示例用法 ---
function runExample() {
    console.log("🚂 === 车厢施工计划优化器 (支持小数日期版本) ===");
    
    // 定义车厢的修建任务，支持小数日期
    const carriages = [{
        id: '车厢A',
        B: 200,
        S: 1,
        E: 4,
        firstDayAmount: 40
    }, {
        id: '车厢B',
        B: 220,
        S: 1,
        E: 13,
        firstDayAmount: 15
    }, {
        id: '车厢C',
        B: 260,
        S: 1.5,  // 小数开始时间
        E: 9,  // 小数结束时间
        firstDayAmount: 10
    }, {
        id: '车厢E',
        B: 240,
        S: 3, // 小数开始时间
        E: 15, // 小数结束时间
        firstDayAmount: 10
    }];
    
    const bestSolution = findBestSolution(carriages, 10);
    
    console.log("\n🏆 === 最佳方案详情 ===");
    console.log(`📊 最佳评分: ${bestSolution.score}`);
    
    console.log("\n--- 最佳方案的每日总修建量 ---");
    bestSolution.totalDailyLoad.forEach((load, day) => {
        console.log(`第 ${day + 1} 天: ${load.toFixed(0)}`);
    });

    console.log("\n--- 最佳方案的各车厢详细修建计划 ---");
    for (const id in bestSolution.finalPlan) {
        const plan = bestSolution.finalPlan[id];
        const total = Object.values(plan).reduce((sum, val) => sum + val, 0);

        console.log(`\n车厢: ${id} (计划总量: ${total})`);
        
        // 按天数排序并输出
        const sortedDays = Object.keys(plan).map(Number).sort((a, b) => a - b);
        for (const day of sortedDays) {
            if (plan[day] > 0) {
                console.log(`  第${day}天: ${plan[day]}`);
            }
        }
    }
}

// 导出主要函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        scheduleConstruction,
        findBestSolution,
        parseDecimalDates,
        convertArrayToDecimalPlan
    };
}

// 如果直接通过 node 运行此文件，则执行示例
if (typeof require !== 'undefined' && require.main === module) {
    runExample();
} 