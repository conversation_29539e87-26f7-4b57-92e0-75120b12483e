
function generateArray(start, cnt, sum, tolerance = 0.02) {
  // --- 新算法：基于间距(gap)的计算 ---

  // 1. 建立数学模型
  // ary = [x1, x2, ..., x_cnt]
  // gaps = [g1, g2, ..., g_cnt] where g1=x1-start, g2=x2-x1, ...
  // x_i = start + g1 + ... + gi
  // sum = cnt*start + cnt*g1 + (cnt-1)*g2 + ... + 1*g_cnt
  //
  // 约束条件: g_i >= 1 且 g_1 <= g_2 <= ... <= g_cnt

  // 2. 计算理论最小和
  // 当所有间距 g_i = 1 时，和最小
  // gapSum_min = cnt*1 + (cnt-1)*1 + ... + 1*1 = cnt * (cnt + 1) / 2
  const minGapSum = cnt * (cnt + 1) / 2;
  const minSum = cnt * start + minGapSum;

  // 3. 根据误差范围，确定目标和
  const upperBound = sum * (1 + tolerance);
  let targetSum = sum;

  if (targetSum < minSum) {
    targetSum = minSum;
  }

  if (targetSum > upperBound) {
    console.error(`无法满足条件：目标和(sum=${sum})过小或无法在误差范围内达成。`);
    console.error(`说明: 理论最小和为 ${minSum}，但允许的最大和仅为 ${upperBound.toFixed(2)}。`);
    return [];
  }

  // 4. 计算需要分配到间距上的"额外值"
  // targetSum = cnt*start + cnt*g1 + ...
  // targetSum - cnt*start = cnt*g1 + ...
  // totalGapSum = targetSum - cnt*start
  const totalGapSum = targetSum - cnt * start;
  let extraGapSum = totalGapSum - minGapSum;

  // 5. 初始化间距数组 (所有间距都为1)
  const gaps = Array(cnt).fill(1);
  const weights = Array.from({length: cnt}, (_, i) => cnt - i);

  // 6. 分配 "额外值" 到间距上（修正后的均匀分配算法）
  // a. 先计算一个基础增长量，让所有间距均匀增加
  const base_add = Math.floor(extraGapSum / minGapSum);
  if (base_add > 0) {
      for (let i = 0; i < cnt; i++) {
          gaps[i] += base_add;
      }
      extraGapSum -= base_add * minGapSum;
  }
  
  // b. 将余下的 extraGapSum 从后往前分配，以保持间距尽可能平滑
  for (let i = cnt - 1; i >= 0; i--) {
      // 尝试给当前 gap[i] 增加 1，看看是否会超出
      if (extraGapSum >= weights[i]) {
          gaps[i]++;
          extraGapSum -= weights[i];
      }
  }

  // 7. 根据最终的间距数组，生成结果数组
  const ary = [];
  let currentVal = start;
  for (let i = 0; i < cnt; i++) {
    currentVal += gaps[i];
    ary.push(currentVal);
  }

  return ary;
}

// 当该文件作为主脚本直接运行时，执行以下代码
if (require.main === module) {
  // 从命令行获取参数
  const args = process.argv.slice(2);
  
  if (args.length < 3) {
    console.log("用法: node generateArray.js <start> <cnt> <sum>");
    console.log("示例 1 (标准): node generateArray.js 10 5 100");
    console.log("示例 2 (sum 过小，将在 2% 误差内自动上调): node generateArray.js 10 5 64");
    process.exit(1); // 退出脚本
  }

  const [start, cnt, sum] = args.map(Number);

  if (isNaN(start) || isNaN(cnt) || isNaN(sum)) {
    console.error("错误：所有输入参数都必须是有效的数字。");
    process.exit(1);
  }

  // 调用核心函数
  const result = generateArray(start, cnt, sum);

  // 格式化输出
  if (result.length > 0) {
    const resultSum = result.reduce((a, b) => a + b, 0);
    const errorPercent = sum === 0 ? 0 : ((resultSum - sum) / sum) * 100;

    console.log(`\n输入参数: start=${start}, cnt=${cnt}, sum=${sum}`);
    console.log(`(和允许有 ±2% 的误差)`);
    console.log("------------------------------------");
    console.log(`输出数组(竖排，便于复制到Excel):`);
    for (const v of result) {
      console.log(v);
    }
    console.log(`实际元素个数: ${result.length}`);
    console.log(`实际元素之和: ${resultSum}`);
    console.log(`与输入 sum 的误差: ${errorPercent.toFixed(2)}%`);
    
    const gaps = [];
    for(let i = 0; i < result.length - 1; i++) {
      gaps.push(result[i+1] - result[i]);
    }
    console.log(`元素间距分布: [${gaps.join(', ')}]`);
    console.log("------------------------------------\n");
  }
}

// 导出函数，以便其他模块可以复用
module.exports = generateArray;
