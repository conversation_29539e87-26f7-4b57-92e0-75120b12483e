/**
 * N维均匀插值函数 - 从start到end生成len个N维点，前密后疏分布
 * 
 * 功能描述：
 * - 输入起始点start [x1, x2, ..., xN]、结束点end [y1, y2, ..., yN]、数组长度len
 * - 返回从start均匀递增到end的len个N维点
 * - 递增规则：按维度优先级递增（类似数字进位系统）
 * - 当不能整除时，采用前密后疏的策略
 * 
 * 示例：
 * - interpolateND([1], [5], 7) -> 1维插值，等同于原来的interpolate
 * - interpolateND([1,1], [3,3], 5) -> 2维插值，等同于interpolate2D
 * - interpolateND([1,1,1], [2,2,2], 4) -> 3维插值
 * 
 * @param {number[]} start - 起始点 [x1, x2, ..., xN]
 * @param {number[]} end - 结束点 [y1, y2, ..., yN]
 * @param {number} len - 返回数组长度（正整数）
 * @returns {number[][]} 插值结果数组
 */
function interpolateND(start, end, len) {
    if (len <= 0) return [];
    if (len === 1) return [start.slice()];

    // 验证维度匹配
    if (start.length !== end.length) {
        throw new Error('起始点和结束点的维度必须相同');
    }

    const dimensions = start.length;

    // 如果是0维（空数组），返回空结果
    if (dimensions === 0) return new Array(len).fill([]);

    // 检查是否所有维度都相同
    const allSame = start.every((val, i) => val === end[i]);
    if (allSame) {
        return new Array(len).fill(start.slice());
    }

    // 生成从起始点到结束点的平衡路径，然后应用前疏后密分布
    const ranges = end.map((endVal, i) => endVal - start[i]);
    const totalSteps = ranges.reduce((sum, range) => sum + Math.abs(range), 0);

    // 如果总步数为0，所有点都相同
    if (totalSteps === 0) {
        return new Array(len).fill(start.slice());
    }

    // 先生成基础路径（每步移动一个单位）
    const pathPoints = [];
    const current = [...start];
    pathPoints.push([...current]);

    // 生成路径上的所有关键点
    while (!current.every((val, i) => val === end[i])) {
        // 找到最需要移动的维度（保持平衡）
        let bestDim = -1;
        let maxNeed = -1;
        let bestCurrentValue = Infinity; // 平手时按当前值更小者优先

        for (let dim = 0; dim < dimensions; dim++) {
            const remaining = end[dim] - current[dim];
            if (remaining > 0) {
                const progress = current[dim] - start[dim];
                const totalRange = end[dim] - start[dim];
                const need = remaining / (totalRange || 1);

                if (need > maxNeed) {
                    maxNeed = need;
                    bestDim = dim;
                    bestCurrentValue = current[dim];
                } else if (need === maxNeed) {
                    // 数值更小者优先（当前值更小的维度优先移动）
                    if (current[dim] < bestCurrentValue) {
                        bestDim = dim;
                        bestCurrentValue = current[dim];
                    }
                }
            }
        }

        if (bestDim !== -1) {
            current[bestDim]++;
            pathPoints.push([...current]);
        } else {
            break;
        }
    }

    const totalPathPoints = pathPoints.length;

    // 如果请求的点数小于等于路径点数，沿整条路径均匀抽样（包含起止点）
    if (len <= totalPathPoints) {
        const sampled = [];
        if (len === 1) {
            sampled.push(pathPoints[0]);
        } else {
            for (let k = 0; k < len; k++) {
                const idx = Math.floor(k * (totalPathPoints - 1) / (len - 1));
                sampled.push(pathPoints[idx]);
            }
        }
        return sampled;
    }

    // 如果请求的点数大于路径点数，应用前疏后密分布
    const result = [];
    const baseAllocation = Math.floor(len / totalPathPoints);
    const extraPositions = len - baseAllocation * totalPathPoints;

    // 为每个路径点分配重复次数
    const allocations = new Array(totalPathPoints).fill(baseAllocation);

    // 将额外的位置分配给前面的点（实现前疏后密）
    for (let i = 0; i < extraPositions; i++) {
        allocations[i]++;
    }

    // 生成最终结果数组
    for (let i = 0; i < totalPathPoints; i++) {
        const point = pathPoints[i];
        for (let j = 0; j < allocations[i]; j++) {
            result.push([...point]);
        }
    }

    return result;
}

/**
 * 生成N维空间中从起始点到结束点的路径
 * @param {number[]} start - 起始点
 * @param {number[]} end - 结束点
 * @returns {number[][]} 路径上的所有点
 */
function generateNDPath(start, end) {
    const dimensions = start.length;
    const ranges = end.map((val, i) => val - start[i]);
    const totalSteps = ranges.reduce((sum, range) => sum + Math.abs(range), 0);

    // 如果没有移动，只返回起始点
    if (totalSteps === 0) {
        return [start.slice()];
    }

    const allPoints = [];
    const current = [...start];
    allPoints.push([...current]);

    // 使用平衡策略：每次选择最需要增长的维度
    while (!current.every((val, i) => val === end[i])) {
        // 找到最需要增长的维度
        let bestDim = -1;
        let maxNeed = -1;
        let bestCurrentValue = Infinity; // 平手时按当前值更小者优先

        for (let dim = 0; dim < dimensions; dim++) {
            const remaining = end[dim] - current[dim];
            const progress = current[dim] - start[dim];
            const totalRange = end[dim] - start[dim];

            if (remaining > 0) {
                // 计算这个维度的"需求度"：剩余距离 / 总距离
                const need = remaining / (totalRange || 1);

                // 先比较需求度，再在平手时比较当前值（小者优先）
                if (need > maxNeed) {
                    maxNeed = need;
                    bestDim = dim;
                    bestCurrentValue = current[dim];
                } else if (need === maxNeed) {
                    if (current[dim] < bestCurrentValue) {
                        bestDim = dim;
                        bestCurrentValue = current[dim];
                    }
                }
            }
        }

        // 如果没有找到需要移动的维度，退出循环
        if (bestDim === -1) break;

        // 在选定的维度上前进一步
        current[bestDim]++;
        allPoints.push([...current]);
    }

    return allPoints;
}

/**
 * 将N维数组格式化为Excel友好的格式
 * @param {number[][]} points - N维点数组
 * @returns {string} 格式化后的字符串
 */
function formatForNDExcel(points) {
    // 使用多个制表符确保Excel能正确识别列分隔
    return points.map(point => point.join('\t')).join('\n');
}

/**
 * 显示Excel友好的输出格式
 * @param {number[][]} points - N维点数组
 */
function displayExcelFormat(points) {
    console.log("=== Excel多列格式 ===");
    console.log("复制下面的内容，在Excel中粘贴：");
    console.log("提示：如果粘贴到一列，请使用 Ctrl+H 查找'制表符'替换为'空格'");
    console.log();
    console.log(formatForNDExcel(points));

    console.log("\n=== 使用说明 ===");
    console.log("方法1: 直接粘贴 → 如果在一列，选中数据 → '数据'菜单 → '分列' → '分隔符' → 勾选'制表符'");
    console.log("方法2: 复制后在Excel中使用'选择性粘贴' → 选择'文本'");
}

/**
 * 解析命令行参数中的向量
 * @param {string[]} args - 命令行参数
 * @param {number} expectedDimensions - 期望的维度数
 * @returns {number[]} 解析后的向量
 */
function parseVector(args, expectedDimensions) {
    if (args.length !== expectedDimensions) {
        throw new Error(`期望 ${expectedDimensions} 个数字，但得到 ${args.length} 个`);
    }

    const vector = args.map(arg => {
        const num = parseInt(arg);
        if (isNaN(num)) {
            throw new Error(`"${arg}" 不是有效的数字`);
        }
        return num;
    });

    return vector;
}

// ========== 预定义参数配置 ==========

/**
 * 连续插值配置列表
 * 每个配置包含关键节点和对应的间隔，自动生成连续的插值路径
 * 
 * 配置格式：
 * {
 *     name: "配置描述", 
 *     keyNodes: [节点1, 节点2, 节点3, ...],  // 关键节点序列
 *     intervals: [段1长度, 段2长度, ...]     // 每段的插值数量
 * }
 * 
 * 说明：
 * - keyNodes.length = intervals.length + 1
 * - 第i个间隔表示从第i个节点到第i+1个节点之间生成多少个点
 */

// ========== 配置选项 ==========
const includeOverlapPoints = false;  // true: 包含重复连接点, false: 跳过重复连接点

// ========== 在这里修改您的插值配置 ==========
const continuousInterpolationConfigs = [
    // {
    //     name: "我的路径：[1,1]→[6,6]→[11,11]→[17,17]→[22,22]",
    //     keyNodes: [
    //         [194],
    //         [246],  
    //     ],
    //     intervals: [40-1]  // 各段长度
    // },
    // {
    //     keyNodes: [
    //         [209],
    //         [221],
    //         [232],
    //         [244],
    //         [255],
    //         [266],
    //         [277],
    //         [288],
    //         [299],
    //         [310],
    //         [320],
    //         [330],
    //         [340],
    //         [350],
    //         [360],
    //         [370],
    //         [380],
    //     ],
    //     intervals: [20 - 1, 19 - 1, 19 - 1, 19 - 1, 19 - 1, 18 - 1, 18 - 1, 18 - 1, 18 - 1, 17 - 1, 17 - 1, 17 - 1,17 - 1, 16 - 1, 16 - 1, 16 - 1]  // 各段长度
    // },
    // {
    //     keyNodes: [
    //         [407],
    //         [434],
    //         [455],
    //     ],
    //     intervals: [20 - 1, 19 - 1]  // 各段长度
    // },
    // {
    //     keyNodes: [
    //         [319],
    //         [327],
    //         [335],
    //         [343],
    //         [350],
    //         [358],
    //         [365],
    //     ],
    //     intervals: [15 - 1, 14-1, 14-1, 14-1, 14-1, 13-1]  // 各段长度
    // },
    // {
    //     keyNodes: [
    //         [257],
    //         [265],
    //         [272],
    //         [280],
    //         [287],
    //         [294],
    //         [301],
    //     ],
    //     intervals: [15 - 1, 14-1, 14-1, 14-1, 14-1, 13-1]  // 各段长度
    // },
    // {
    //     keyNodes: [
    //         [66],
    //         [88]  ,
    //         [109],
    //         [130],
    //         [151],
    //         [172],
    //         [191],
    //         [211],
    //         [231],
    //         [251],
    //         [270],
    //         [288],
    //         [307],
    //         [326],
    //         [343],
    //         [361],
    //         [378],
    //         [396],
    //         [413],
    //         [420],
    //     ],
    //     intervals: [20 - 1, 19 - 1, 19 - 1, 19 - 1, 19 - 1, 18 - 1, 18 - 1, 18 - 1, 18 - 1, 17 - 1, 17 - 1, 17 - 1, 17 - 1, 16 - 1, 16 - 1, 16 - 1, 16 - 1, 15 - 1, 15 - 1]  // 各段长度
    // },

    // {
    //     keyNodes: [
    //         [245],
    //         [257],
    //         [268],
    //         [279],
    //         [290],
    //         [302],
    //         [312],
    //     ],
    //     intervals: [15 - 1, 14-1, 14-1, 14-1, 14-1, 13-1]  // 各段长度
    // },

    // {
    //     keyNodes: [
    //         [120],
    //         [131],
    //         [142],
    //         [152],
    //         [163],
    //         [173],
    //         [183],
    //     ],
    //     intervals: [15 - 1, 14-1, 14-1, 14-1, 14-1, 13-1]  // 各段长度
    // },


    // {
    //     keyNodes: [
    //         [23],
    //         [30],
    //         [37],
    //         [44],
    //         [51],
    //         [58],
    //         [65],
    //     ],
    //     intervals: [15 - 1, 14-1, 14-1, 14-1, 14-1, 13-1]  // 各段长度
    // },

    // {
    //     keyNodes: [
    //         [254],
    //         [264],
    //         [274],
    //         [284],
    //         [294],
    //         [304],
    //         [314],
    //         [324],
    //         [334],
    //         [344],
    //         [356],
    //         [366],
    //     ],
    //     intervals: [16 - 1, 16-1, 15 - 1, 15 - 1, 15 - 1, 15-1, 14-1, 14-1, 14-1, 14-1, 13-1]  // 各段长度
    // },

    // {
    //     keyNodes: [
    //         [210, 210, 210],
    //         [221, 221, 221],
    //         [223, 223, 223],
    //         [225, 225, 225],
    //         [233, 233, 233],
    //         [242, 242, 242],
    //         [260, 260, 260],
    //     ],
    //     intervals: [15 - 1, 14-1, 14-1, 14-1, 14-1, 13-1]  // 各段长度
    // },

    // {
    //     keyNodes: [
    //         [2, 2],
    //         [10, 10]
    //     ],
    //     intervals: [19]  // 各段长度 
    // }

    {
        keyNodes: [
            [177],
            [197]
        ],
        intervals: [29]
    },

    //     {
    //     keyNodes: [
    //         [68, 68, 67, 67, 67],
    //         [70,	69,	69,	69,     	69],
    //         [71, 71, 71, 71, 71],
    //         [73, 73, 73, 73, 72],
    //         [75, 75, 75, 75, 74],
    //         [77, 77, 77, 77, 76],
    //         [79, 79, 79, 79, 79],
    //         [82, 81, 81, 81, 81],
    //     ],
    //     intervals: [15-1,15 - 1, 14-1, 14-1, 14-1, 14-1, 13-1]  // 各段长度
    // }

            {
        keyNodes: [
            [8, 8, 8, 8,  8],
            [19, 19, 19, 19, 19],
        ],
        intervals: [13]  // 各段长度
    }


    // {
    //     keyNodes: [
    //         [48],
    //         [145],
    //         // [177]
    //     ],
    //     intervals: [82]
    // }

];

// ==============================================

/**
 * 执行连续插值配置
 */
function runContinuousInterpolation() {
    console.log("=== 执行连续插值任务 ===\n");

    continuousInterpolationConfigs.forEach((config, configIndex) => {
        console.log(`### 任务 ${configIndex + 1}: ${config.name} ###`);

        try {
            // 验证配置
            if (config.keyNodes.length !== config.intervals.length + 1) {
                throw new Error(`关键节点数量(${config.keyNodes.length})应该比间隔数量(${config.intervals.length})多1`);
            }

            // 收集所有结果用于最终合并输出
            let allResults = [];

            // 为每个区间生成插值
            for (let i = 0; i < config.intervals.length; i++) {
                const start = config.keyNodes[i];
                const end = config.keyNodes[i + 1];
                const intervalCount = config.intervals[i]; // 中间点数量

                // 总长度 = 中间点数 + 起点 + 终点
                const totalLen = intervalCount + 2;
                const result = interpolateND(start, end, totalLen);

                // 收集结果（根据配置决定是否包含重复连接点）
                if (includeOverlapPoints) {
                    // 包含所有点（包括重复的连接点）
                    allResults.push(...result);
                } else {
                    // 跳过重复的连接点
                    if (i === 0) {
                        allResults.push(...result);
                    } else {
                        allResults.push(...result.slice(1)); // 跳过起始点
                    }
                }
            }

            // 显示路径信息
            const pathDescription = config.keyNodes.map(node => `[${node.join(',')}]`).join(' → ');
            const intervalSum = config.intervals.reduce((sum, len) => sum + len, 0);
            const expectedTotal = intervalSum + config.keyNodes.length; // 间隔总和 + 关键节点数量
            console.log(`路径: ${pathDescription}`);
            console.log(`段数: ${config.intervals.length}`);
            console.log(`间隔总和: ${intervalSum}, 关键节点数: ${config.keyNodes.length}`);
            console.log(`期望总数: ${intervalSum} + ${config.keyNodes.length} = ${expectedTotal}`);
            console.log(`实际总点数: ${allResults.length} ${includeOverlapPoints ? '(包含重复连接点)' : '(跳过重复连接点)'}\n`);

            // 输出完整结果
            if (allResults.length > 0 && allResults[0].length === 1) {
                console.log(allResults.map(point => point[0]).join('\n'));
            } else {
                console.log(formatForNDExcel(allResults));
            }

        } catch (error) {
            console.error(`任务执行失败: ${error.message}`);
        }

        console.log("\n" + "=".repeat(50) + "\n"); // 任务间分隔
    });
}

// ========== 命令行参数处理 ==========

// 获取命令行参数
const args = process.argv.slice(2);

// 如果没有命令行参数，执行连续插值配置
if (args.length === 0) {
    runContinuousInterpolation();
    process.exit(0);
}

// 检查基本参数数量
if (args.length < 3) {
    console.log("使用方法: node interpolationND.js <start1> [start2] ... <end1> [end2] ... <interval>");
    console.log("说明: 自动根据参数数量判断维度，interval表示中间点数量（不包含起点和终点）");
    console.log("示例:");
    console.log("  1维: node interpolationND.js 1 5 3  → 1到5中间3个点，总共5个点");
    console.log("  2维: node interpolationND.js 1 1 3 3 3  → [1,1]到[3,3]中间3个点，总共5个点");
    console.log("  3维: node interpolationND.js 1 1 1 2 2 2 2  → [1,1,1]到[2,2,2]中间2个点，总共4个点");
    console.log("\n或者直接运行 node interpolationND.js 执行连续插值任务");
    process.exit(1);
}

try {
    // 自动推断维度：参数总数 = 2*dimensions + 1（len）
    // 所以 dimensions = (args.length - 1) / 2
    const totalArgs = args.length;
    if ((totalArgs - 1) % 2 !== 0) {
        throw new Error("参数数量不正确。应该是: start向量 + end向量 + len");
    }

    const dimensions = (totalArgs - 1) / 2;

    if (dimensions < 1) {
        throw new Error("至少需要1维");
    }

    // 解析start向量
    const startArgs = args.slice(0, dimensions);
    const start = parseVector(startArgs, dimensions);

    // 解析end向量
    const endArgs = args.slice(dimensions, 2 * dimensions);
    const end = parseVector(endArgs, dimensions);

    // 解析间隔数（中间点数量）
    const intervalCount = parseInt(args[2 * dimensions]);
    if (isNaN(intervalCount) || intervalCount < 0) {
        throw new Error("间隔数必须是大于等于0的整数");
    }

    // 计算总点数：间隔数 + 起点 + 终点
    const totalLen = intervalCount + 2;

    // 计算并输出结果
    const result = interpolateND(start, end, totalLen);

    // 如果是1维，使用简单格式
    if (result.length > 0 && result[0].length === 1) {
        console.log(result.map(point => point[0]).join('\n'));
    } else {
        // 多维使用Excel格式说明
        displayExcelFormat(result);
    }

} catch (error) {
    console.error("错误:", error.message);
    console.log("\n使用方法: node interpolationND.js <start1> [start2] ... <end1> [end2] ... <interval>");
    console.log("说明: 自动根据参数数量判断维度，interval表示中间点数量（不包含起点和终点）");
    console.log("示例:");
    console.log("  1维: node interpolationND.js 1 5 3  → 1到5中间3个点，总共5个点");
    console.log("  2维: node interpolationND.js 1 1 3 3 3  → [1,1]到[3,3]中间3个点，总共5个点");
    console.log("  3维: node interpolationND.js 1 1 1 2 2 2 2  → [1,1,1]到[2,2,2]中间2个点，总共4个点");
    process.exit(1);
}

// 导出函数（支持Node.js模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { interpolateND, generateNDPath, formatForNDExcel };
} 