function genMiningSequence(a, b, steps = 200, wobble = 0.25, minYield = 1) {
    const ease = t => t * t * (3 - 2 * t); // smoothstep，可改成其它缓动
    const m = Array.from({ length: steps }, (_, i) => {
      const t = (i + 0.5) / steps;
      return a + (b - a) * ease(t);
    });
  
    // 为每一步设一个上下边界（围绕目标期望的带宽）
    const lo = m.map(x => Math.max(minYield, Math.floor(x * (1 - wobble))));
    const hi = m.map((x, i) => Math.max(lo[i], Math.ceil(x * (1 + wobble))));
  
    // 目标总量（四舍五入到整数总量）
    const totalTarget = Math.round(m.reduce((s, x) => s + x, 0));
  
    // 预计算“未来最小/最大可能总和”，用于可行域裁剪
    const stepsN = steps;
    const futureLo = new Array(stepsN + 1).fill(0);
    const futureHi = new Array(stepsN + 1).fill(0);
    for (let i = stepsN - 1; i >= 0; i--) {
      futureLo[i] = futureLo[i + 1] + lo[i];
      futureHi[i] = futureHi[i + 1] + hi[i];
    }
  
    let remain = totalTarget;
    const out = new Array(stepsN);
    for (let i = 0; i < stepsN - 1; i++) {
      const target = m[i];
      const band = Math.max(1, (hi[i] - lo[i]) / 2);
      const noise = (Math.random() - Math.random()) * band; // 零均值三角分布
      let v = Math.round(target + noise);
  
      // 可行域裁剪：保证剩余步数仍可凑到精确总和
      const minAllowed = Math.max(lo[i], remain - futureHi[i + 1]);
      const maxAllowed = Math.min(hi[i], remain - futureLo[i + 1]);
      v = Math.min(maxAllowed, Math.max(minAllowed, v));
  
      out[i] = v;
      remain -= v;
    }
    // 最后一位用剩余预算收尾
    out[stepsN - 1] = Math.max(lo[stepsN - 1], Math.min(hi[stepsN - 1], remain));
    return out;
  }

  // 基于上一段期望 Eprev，实时计算本段第 k 次的值（无状态，期望正确）
  function valueAtWithPrevMean(Eprev, Ei, k, steps, { wobble = 0.1, minYield = 1 } = {}) {
    const ease = t => t * t * (3 - 2 * t); // smoothstep
    const s = Eprev;
    const e = 2 * Ei - s; // 保证段均值=Ei

    const t = (k + 0.5) / steps;
    const m = s + (e - s) * ease(t); // 段内平滑目标

    // 边界抑制的零均值噪声（更平滑）
    const envelope = Math.sin(Math.PI * t); // t=0/1 为0
    const noise = (Math.random() - Math.random()) * (wobble * Ei) * envelope;

    return Math.max(minYield, Math.round(m + noise));
  }

  // 采样一个段，返回数组与统计信息
  function sampleSegmentWithPrevMean(Eprev, Ei, steps, options) {
    const arr = Array.from({ length: steps }, (_, k) => valueAtWithPrevMean(Eprev, Ei, k, steps, options));
    const sum = arr.reduce((s, x) => s + x, 0);
    const mean = sum / steps;
    const min = Math.min(...arr);
    const max = Math.max(...arr);
    return { arr, sum, mean, min, max };
  }

  // 生成“本段均值固定为 mean”的序列（严格满足总量≈mean*steps），并尽量平滑接缝
  function genLevelSequenceFixedMean(mean, steps = 200, sHint = null, wobble = 0.25, minYield = 1) {
    const ease = t => t * t * (3 - 2 * t);

    let s = sHint == null ? mean : sHint;
    let e = 2 * mean - s; // 由 (s + e) / 2 = mean 得出

    const m = Array.from({ length: steps }, (_, i) => {
      const t = (i + 0.5) / steps;
      return s + (e - s) * ease(t);
    });

    const totalTarget = Math.round(mean * steps);

    const lo = m.map(x => Math.max(minYield, Math.floor(x * (1 - wobble))));
    const hi = m.map((x, i) => Math.max(lo[i], Math.ceil(x * (1 + wobble))));

    const n = steps;
    const futureLo = new Array(n + 1).fill(0);
    const futureHi = new Array(n + 1).fill(0);
    for (let i = n - 1; i >= 0; i--) {
      futureLo[i] = futureLo[i + 1] + lo[i];
      futureHi[i] = futureHi[i + 1] + hi[i];
    }

    let remain = totalTarget;
    const out = new Array(n);
    for (let i = 0; i < n - 1; i++) {
      const band = Math.max(1, (hi[i] - lo[i]) / 2);
      let v = Math.round(m[i] + (Math.random() - Math.random()) * band);

      const minAllowed = Math.max(lo[i], remain - futureHi[i + 1]);
      const maxAllowed = Math.min(hi[i], remain - futureLo[i + 1]);
      v = Math.min(maxAllowed, Math.max(minAllowed, v));

      out[i] = v;
      remain -= v;
    }
    out[n - 1] = Math.max(lo[n - 1], Math.min(hi[n - 1], remain));

    return { seq: out, end: out[n - 1] };
  }

  // 串接多等级（每等级均值=E[i]），steps 为每等级步长
  function buildAllLevelsFixedMean(E, steps = 200, wobble = 0.25, minYield = 1, options = {}) {
    const { startPolicy = 'prev_end', blendAlpha = 0.7 } = options; // startPolicy: 'prev_end' | 'blend'
    const all = [];
    let sHint = null;
    for (let i = 0; i < E.length; i++) {
      const { seq, end } = genLevelSequenceFixedMean(E[i], steps, sHint, wobble, minYield);
      all.push(...seq);
      // 计算下一段的起点策略
      if (i < E.length - 1) {
        if (startPolicy === 'prev_end') {
          sHint = end; // 严格连续，无跳变
        } else {
          // 与当前段均值做插值，避免 e 过大/过小
          sHint = end * blendAlpha + E[i] * (1 - blendAlpha);
        }
      }
    }
    return all;
  }

  // 直接构建“分段”结果：返回 segments（长度为 n，每段长度=steps）与扁平序列 flat
  function buildSegmentsFixedMean(E, steps = 200, wobble = 0.25, minYield = 1, options = {}) {
    const segments = [];
    let sHint = null;
    for (let i = 0; i < E.length; i++) {
      const { seq, end } = genLevelSequenceFixedMean(E[i], steps, sHint, wobble, minYield);
      segments.push(seq);
      if (i < E.length - 1) {
        const { startPolicy = 'prev_end', blendAlpha = 0.7 } = options;
        if (startPolicy === 'prev_end') {
          sHint = end;
        } else {
          sHint = end * blendAlpha + E[i] * (1 - blendAlpha);
        }
      }
    }
    const flat = segments.flat();
    return { segments, flat };
  }

  // 将已生成的扁平序列按固定步长拆成分段
  function chunkSequence(seq, steps) {
    const out = [];
    for (let i = 0; i < seq.length; i += steps) out.push(seq.slice(i, i + steps));
    return out;
  }

  // 纯函数：生成每段的摘要信息（不修改原数组）
  function summarizeSegments(segments, E, steps) {
    return segments.map((seg, i) => {
      const sum = seg.reduce((s, x) => s + x, 0);
      const mean = sum / steps;
      const start = i * steps;
      const end = start + seg.length - 1;
      return { index: i, expect: E[i], start, end, sum, mean };
    });
  }

  // 打印完整数值（不截断）：包含扁平序列和每段的全部值
  function printFullValues(segments, flat, E, steps) {
    console.log('================ FULL FLAT SEQUENCE ================');
    console.log(flat);
    console.log('================ PER-SEGMENT VALUES ===============');
    let offset = 0;
    for (let i = 0; i < segments.length; i++) {
      const seg = segments[i];
      console.log(`[segment ${i}] expect=${E[i]}  indexRange=[${offset}, ${offset + seg.length - 1}]  length=${seg.length}`);
      console.log(seg);
      offset += seg.length;
    }
  }

  // 校验每段的总量与均值是否匹配给定期望
  function verifySegments(seq, E, steps, log = true) {
    const sums = [];
    const avgs = [];
    const targetSums = E.map(x => Math.round(x * steps));
    let pass = true;
    for (let i = 0; i < E.length; i++) {
      const start = i * steps;
      const end = start + steps;
      const part = seq.slice(start, end);
      const sum = part.reduce((s, x) => s + x, 0);
      const avg = sum / steps;
      sums.push(sum);
      avgs.push(avg);
      if (sum !== targetSums[i]) pass = false;
    }
    if (log) {
      console.log('[verify] target sums:', targetSums);
      console.log('[verify] actual sums:', sums);
      console.log('[verify] segment means:', avgs);
      console.log('[verify] pass:', pass);
    }
    return { pass, sums, avgs, targetSums };
  }

  // 示例：使用 valueAtWithPrevMean 实时计算，并采样 100 点验证
  const E = [1.1, 2];
  const steps = 100;
  const opt = { wobble: 0.1, minYield: 1 };

  // 第一段：没有上一段，取 Eprev=E[0]
  const res0 = sampleSegmentWithPrevMean(E[0], E[0], steps, opt);
  console.log('[segment 0] expect=', E[0], ' mean=', res0.mean, ' min=', res0.min, ' max=', res0.max);
  console.log('[segment 0 values]', res0.arr);

  // 第二段：上一段期望=E[0]，本段期望=E[1]
  const res1 = sampleSegmentWithPrevMean(E[0], E[1], steps, opt);
  console.log('[segment 1] expect=', E[1], ' mean=', res1.mean, ' min=', res1.min, ' max=', res1.max);
  console.log('[segment 1 values]', res1.arr);
