/**
 * 模拟单次运输所有物品需要多少趟
 * @param {number} n - 物品总数
 * @param {number} x - 背包容量
 * @param {number} y - 物品重量的上限，重量随机范围为 [y/3, y]
 * @returns {number} - 本次模拟所需的运输趟数
 */
function simulateOneRun(n, x, y) {
    const minWeight = y / 3;

    // 假设背包容量至少能装下最重的单个物品，否则问题无解
    if (x < y) {
        // 如果背包连可能出现的最重物品都装不下，
        // 我们可以返回一个错误或者无穷大，这里简单打印一个警告。
        console.warn(`警告: 背包容量 x=${x} 小于最大可能重量 y=${y}。某些物品可能永远无法运送。`);
    }
    if (x < minWeight) {
        // 如果背包容量小于最小物品重量，则一个也运不走
        return Infinity;
    }
    
    // 1. 生成所有n个物品的随机重量
    const weights = Array.from({ length: n }, () => Math.random() * (y - minWeight) + minWeight);

    const transported = new Array(n).fill(false);
    let itemsTransportedCount = 0;
    let trips = 0;

    // 2. 只要还有物品没运走，就继续开辟新行程
    while (itemsTransportedCount < n) {
        trips++;
        let currentCapacity = x;
        
        // 3. 每次行程都尝试从所有【还未运输】的物品中进行装载
        for (let i = 0; i < n; i++) {
            if (!transported[i] && weights[i] <= currentCapacity) {
                transported[i] = true;
                currentCapacity -= weights[i];
                itemsTransportedCount++;
            }
        }
    }

    return trips;
}

/**
 * 通过多次模拟计算运输趟数的期望值
 * @param {number} n - 物品总数
 * @param {number} x - 背包容量
 * @param {number} y - 物品重量的上限
 * @param {number} [simulationCount=10000] - 模拟次数，次数越多结果越精确
 * @returns {number} - 运输趟数的期望值
 */
function calculateExpectedTrips(n, x, y, simulationCount = 10000) {
    let totalTrips = 0;
    for (let i = 0; i < simulationCount; i++) {
        totalTrips += simulateOneRun(n, x, y);
    }

    return totalTrips / simulationCount;
}

// --- 使用示例 ---
// 固定物品数量为6
const n = 6;

// 在这里配置您想要计算的所有场景
const configurations = [
    // { x: 75, y: 50 },

    // { x: 120, y: 75 },
    // { x: 150, y: 75 },

    // { x: 180, y: 120 },
    // { x: 210, y: 120 },
    // { x: 250, y: 120 },

    // { x: 300, y: 200 },
    // { x: 360, y: 200 },
    // { x: 420, y: 200 },

    // { x: 420, y: 280 },
    // { x: 480, y: 280 },
    // { x: 540, y: 280 },
    // { x: 600, y: 280 },

    // { x: 600, y: 400 },
    // { x: 660, y: 400 },
    // { x: 720, y: 400 },
    // { x: 780, y: 400 },
    // { x: 840, y: 400 },

    { x: 840, y: 560 },
    { x: 910, y: 560 },
    { x: 990, y: 560 },
    { x: 1080, y: 560 },
    { x: 1180, y: 560 },

    // { x: 960, y: 640 },
    // { x: 910, y: 560 },
    // { x: 990, y: 560 },
    // { x: 1080, y: 560 },
    // { x: 1180, y: 560 },

];

function runSimulations() {
    console.log(`开始批量计算，物品数量固定为: ${n}`);
    console.log("------------------------------------------");

    configurations.forEach(config => {
        const { x, y } = config;
        
        if (typeof x !== 'number' || typeof y !== 'number' || isNaN(x) || isNaN(y)) {
            console.log(`配置无效，已跳过: { x: ${x}, y: ${y} }`);
            return;
        }

        const expectedTrips = calculateExpectedTrips(n, x, y);
        console.log(`计算配置: 背包容量 x=${x}, 重量上限 y=${y}`, `  => 期望趟数: ${expectedTrips.toFixed(4)}`);
    });
}

// 运行所有模拟
runSimulations();
