import xlsx from "json-as-xlsx"
import { assetsMgr } from "./AssetsMgr"
import Battle from "./Battle"
import { BattleRoleType } from "./BattleEnum"
import BattleRole from "./BattleRole"
import PassengerModel from "./PassengerModel"
import { cfgHelper } from "./cfgHelper"
import { util } from "./utils/Utils"

require("./extend/ExtendArray")

const LV = 60, STAR_LV = 3

// 遗传算法参数配置
interface GAConfig {
    populationSize: number  // 种群大小
    generations: number     // 进化代数
    crossoverRate: number   // 交叉概率
    mutationRate: number    // 变异概率
    eliteSize: number       // 精英保留数量
    teamSize: number        // 阵容大小
}

// 个体（阵容）定义
interface Individual {
    genes: number[]         // 基因（怪物ID数组）
    fitness: number         // 适应度（胜率）
    battleRoles?: BattleRole[]  // 战斗角色（缓存）
}

class GeneticAlgorithmBattle {
    private config: GAConfig
    private monsterPool: number[]  // 可用怪物池
    private population: Individual[] = []
    private testTeams: number[][] = []  // 测试队伍
    private generation = 0
    private roleCache = {}  // 角色缓存，避免重复创建对象，大幅提升性能
                               // 每个角色只创建一次，后续使用reset()重置状态
    
    constructor(config: GAConfig) {
        this.config = config
    }

    async init() {
        console.log("🚀 开始初始化遗传算法...")
        
        // 初始化资源
        console.log("📚 加载游戏资源...")
        assetsMgr.init()
        cfgHelper.init()
        
        // 准备数据
        console.log("🔍 准备基础数据...")
        await this.prepareData()
        
        // 初始化种群
        console.log("🧬 创建初始种群...")
        this.initializePopulation()
        
        console.log(`✅ 遗传算法初始化完成`)
        console.log(`📊 配置信息:`)
        console.log(`   - 种群大小: ${this.config.populationSize}`)
        console.log(`   - 进化代数: ${this.config.generations}`)
        console.log(`   - 阵容大小: ${this.config.teamSize}`)
        console.log(`   - 可用怪物: ${this.monsterPool.length}`)
        console.log(`   - 测试队伍: ${this.testTeams.length}`)
        
        // 计算总战斗次数预估
        let totalBattles = this.config.populationSize * this.testTeams.length * this.config.generations
        console.log(`⚔️  预估总战斗次数: ${totalBattles.toLocaleString()} 场`)
        console.log(`⏱️  预估运行时间: ${Math.round(totalBattles / 10000)} - ${Math.round(totalBattles / 5000)} 分钟`)
    }

    private async prepareData() {
        // 获取基础数据
        let testData: any[] = assetsMgr.getJson("BattleTeamsTest30").datas

        this.monsterPool = assetsMgr.getJson("Character").datas.map(m => m.id)
  

        // 准备测试队伍
        this.testTeams = testData.map(t => t.monster || t.team || [])
        
        console.log(`准备数据完成，怪物池大小: ${this.monsterPool.length}`)
    }

    // 初始化种群
    private initializePopulation() {
        this.population = []
        for (let i = 0; i < this.config.populationSize; i++) {
            let genes = this.generateRandomGenes()
            let individual: Individual = {
                genes: genes,
                fitness: 0
            }
            this.population.push(individual)
        }
    }

    // 生成随机基因（阵容）
    private generateRandomGenes(): number[] {
        let genes = []
        let availableMonsters = [...this.monsterPool]
        
        for (let i = 0; i < this.config.teamSize; i++) {
            let randomIndex = Math.floor(Math.random() * availableMonsters.length)
            genes.push(availableMonsters[randomIndex])
            availableMonsters.splice(randomIndex, 1) // 避免重复
        }
        
        return genes
    }

    // 计算适应度（胜率）
    private async calculateFitness(individual: Individual): Promise<number> {
        let monsters = this.createBattleRoles(individual.genes)
        individual.battleRoles = monsters
        
        let winCount = 0
        let totalBattles = this.testTeams.length
        let battleCount = 0
        
        console.log(`  开始与 ${totalBattles} 支测试队伍战斗...`)
        
        for (let i = 0; i < this.testTeams.length; i++) {
            let testTeam = this.testTeams[i]
            battleCount++
            
            // 每100场战斗打印一次进度
            if (battleCount % 100 === 0 || battleCount === 1) {
                console.log(`    战斗进度: ${battleCount}/${totalBattles} (${(battleCount/totalBattles*100).toFixed(1)}%)`)
            }
            
            let passengers = this.createPassengerRoles(testTeam)
            // 获取重置状态的怪物角色，使用缓存优化性能
            let monsters = this.createBattleRoles(individual.genes)
            let [pCnt, mCnt] = this.executeBattle(passengers, monsters)
            
            if (pCnt > mCnt) {
                winCount++
            }
            
            // 每50场战斗让出一点执行时间
            if (battleCount % 50 === 0) {
                await util.wait(1)
            }
        }
        
        let fitness = winCount / totalBattles
        console.log(`  战斗完成！胜利: ${winCount}/${totalBattles}, 胜率: ${(fitness * 100).toFixed(2)}%`)
        
        return fitness
    }

    // 创建怪物战斗角色（使用缓存优化）
    private createBattleRoles(passengerIds: number[]): BattleRole[] {
        return passengerIds.map(id => {
            let cacheKey = `passenger_${id}_m`
            if (!this.roleCache[cacheKey]) {
                // 只在第一次创建角色对象
                let passenger = new PassengerModel().init({ id, level: LV, starLv: STAR_LV })
                let role = new BattleRole().initData({
                    id: passenger.getID(),
                    hp: passenger.getHp(),
                    attack: passenger.getAttack(),
                    skills: passenger.getSkills(),
                    role: passenger
                })
                role.mergeSkills()
                role.type = BattleRoleType.PASSENGER
                this.roleCache[cacheKey] = role
            }
            // 每次使用前重置状态，避免状态污染
            return this.roleCache[cacheKey].reset()
        })
    }

    // 创建玩家角色（使用缓存优化）
    private createPassengerRoles(passengerIds: number[]): BattleRole[] {
        return passengerIds.map(id => {
            let cacheKey = `passenger_${id}`
            if (!this.roleCache[cacheKey]) {
                // 只在第一次创建角色对象
                let passenger = new PassengerModel().init({ id, level: LV, starLv: STAR_LV })
                let role = new BattleRole().initData({
                    id: passenger.getID(),
                    hp: passenger.getHp(),
                    attack: passenger.getAttack(),
                    skills: passenger.getSkills(),
                    role: passenger
                })
                role.mergeSkills()
                role.type = BattleRoleType.PASSENGER
                this.roleCache[cacheKey] = role
            }
            // 每次使用前重置状态，避免状态污染
            return this.roleCache[cacheKey].reset()
        })
    }

    // 执行战斗
    private executeBattle(passengers: BattleRole[], monsters: BattleRole[]): [number, number] {
        try {
            let battle = new Battle()
            battle.openDebug = false
            battle.openLog = false
            let result = battle.init(passengers, monsters)
            
            // 计算存活数量
            let pCnt = passengers.filter(p => p.hp > 0).length
            let mCnt = monsters.filter(m => m.hp > 0).length
            
            return [pCnt, mCnt]
        } catch (error) {
            // 处理战斗超时或其他错误
            if (error.message && error.message.includes("round over")) {
                // 战斗回合数超限，统计当前存活情况
                let pCnt = passengers.filter(p => p.hp > 0).length
                let mCnt = monsters.filter(m => m.hp > 0).length
                return [pCnt, mCnt]
            } else {
                console.error("战斗执行错误:", error.message || error)
                return [0, 1] // 默认失败
            }
        }
    }

    // 选择操作（轮盘赌选择）
    private selection(): Individual[] {
        let selected: Individual[] = []
        let totalFitness = this.population.reduce((sum, ind) => sum + ind.fitness, 0)
        
        for (let i = 0; i < this.config.populationSize; i++) {
            let random = Math.random() * totalFitness
            let sum = 0
            
            for (let individual of this.population) {
                sum += individual.fitness
                if (sum >= random) {
                    selected.push(individual)
                    break
                }
            }
        }
        
        return selected
    }

    // 交叉操作
    private crossover(parent1: Individual, parent2: Individual): [Individual, Individual] {
        if (Math.random() > this.config.crossoverRate) {
            return [parent1, parent2]
        }
        
        let crossoverPoint = Math.floor(Math.random() * this.config.teamSize)
        
        let child1Genes = [
            ...parent1.genes.slice(0, crossoverPoint),
            ...parent2.genes.slice(crossoverPoint)
        ]
        
        let child2Genes = [
            ...parent2.genes.slice(0, crossoverPoint),
            ...parent1.genes.slice(crossoverPoint)
        ]
        
        // 去重处理
        child1Genes = this.removeDuplicates(child1Genes)
        child2Genes = this.removeDuplicates(child2Genes)
        
        return [
            { genes: child1Genes, fitness: 0 },
            { genes: child2Genes, fitness: 0 }
        ]
    }

    // 变异操作
    private mutation(individual: Individual): Individual {
        if (Math.random() > this.config.mutationRate) {
            return individual
        }
        
        let mutatedGenes = [...individual.genes]
        let mutationPoint = Math.floor(Math.random() * this.config.teamSize)
        
        // 随机替换一个基因
        let availableMonsters = this.monsterPool.filter(id => !mutatedGenes.includes(id))
        if (availableMonsters.length > 0) {
            let randomMonster = availableMonsters[Math.floor(Math.random() * availableMonsters.length)]
            mutatedGenes[mutationPoint] = randomMonster
        }
        
        return { genes: mutatedGenes, fitness: 0 }
    }

    // 去重并补充基因
    private removeDuplicates(genes: number[]): number[] {
        let uniqueGenes = [...new Set(genes)]
        
        // 如果去重后数量不足，随机补充
        while (uniqueGenes.length < this.config.teamSize) {
            let availableMonsters = this.monsterPool.filter(id => !uniqueGenes.includes(id))
            if (availableMonsters.length > 0) {
                let randomMonster = availableMonsters[Math.floor(Math.random() * availableMonsters.length)]
                uniqueGenes.push(randomMonster)
            } else {
                break
            }
        }
        
        return uniqueGenes.slice(0, this.config.teamSize)
    }

    // 主要进化循环
    async evolve() {
        console.log("开始进化过程...")
        
        for (this.generation = 0; this.generation < this.config.generations; this.generation++) {
            console.log(`\n=== 第 ${this.generation + 1} 代 ===`)
            
            // 计算当前种群的适应度
            await this.evaluatePopulation()
            
            // 排序（按适应度降序）
            this.population.sort((a, b) => b.fitness - a.fitness)
            
            // 输出当前代的最佳结果
            this.printGenerationStats()
            
            // 如果不是最后一代，进行进化操作
            if (this.generation < this.config.generations - 1) {
                let newPopulation: Individual[] = []
                
                // 精英保留
                for (let i = 0; i < this.config.eliteSize; i++) {
                    newPopulation.push({ ...this.population[i] })
                }
                
                // 生成新的个体
                while (newPopulation.length < this.config.populationSize) {
                    let selected = this.selection()
                    let parent1 = selected[Math.floor(Math.random() * selected.length)]
                    let parent2 = selected[Math.floor(Math.random() * selected.length)]
                    
                    let [child1, child2] = this.crossover(parent1, parent2)
                    child1 = this.mutation(child1)
                    child2 = this.mutation(child2)
                    
                    newPopulation.push(child1)
                    if (newPopulation.length < this.config.populationSize) {
                        newPopulation.push(child2)
                    }
                }
                
                this.population = newPopulation
            }
        }
        
        console.log("\n=== 进化完成 ===")
        await this.exportResults()
    }

    // 评估整个种群的适应度
    private async evaluatePopulation() {
        let count = 0
        let total = this.population.filter(ind => ind.fitness === 0).length
        
        console.log(`开始评估 ${total} 个个体的适应度...`)
        
        for (let i = 0; i < this.population.length; i++) {
            let individual = this.population[i]
            if (individual.fitness === 0) { // 只计算未计算过的个体
                count++
                console.log(`正在评估第 ${count}/${total} 个个体 (索引${i})...`)
                
                let startTime = Date.now()
                individual.fitness = await this.calculateFitness(individual)
                let duration = Date.now() - startTime
                
                console.log(`个体 ${count} 完成评估，胜率: ${(individual.fitness * 100).toFixed(2)}%, 耗时: ${duration}ms`)
                
                // 每处理一定数量后等待，避免阻塞
                if (count % 5 === 0) {
                    await util.wait(50)
                    console.log(`已完成 ${count}/${total} 个个体的评估`)
                }
            }
        }
        
        console.log(`所有 ${total} 个个体评估完成！`)
    }

    // 打印当前代的统计信息
    private printGenerationStats() {
        let best = this.population[0]
        let avgFitness = this.population.reduce((sum, ind) => sum + ind.fitness, 0) / this.population.length
        
        console.log(`最佳胜率: ${(best.fitness * 100).toFixed(2)}%`)
        console.log(`平均胜率: ${(avgFitness * 100).toFixed(2)}%`)
        console.log(`最佳阵容: ${best.genes.map(id => {
            let passenger = assetsMgr.getJsonData("Character", id)
            return assetsMgr.lang(passenger.name)
        }).join(", ")}`)
    }

    // 导出结果到Excel
    private async exportResults() {
        let outs = {
            "代数": [],
            "胜率": [],
            "怪物1": [],
            "怪物2": [],
            "怪物3": [],
            "怪物4": [],
            "怪物5": []
        }

        // 取前50个最佳阵容
        let topResults = this.population.slice(0, Math.min(50, this.population.length))
        
        for (let i = 0; i < topResults.length; i++) {
            let individual = topResults[i]
            outs["代数"].push(this.generation + 1)
            outs["胜率"].push(individual.fitness)
            
            for (let j = 0; j < this.config.teamSize; j++) {
                let passenger = assetsMgr.getJsonData("Character", individual.genes[j])
                outs[`怪物${j + 1}`].push(assetsMgr.lang(passenger.name))
            }
        }

        this.toExcel(outs, "GeneticAlgorithmResults")
    }

    // 导出到Excel的辅助方法
    private toExcel(data, fileName: string) {
        let sheet = { columns: [], content: [] }
        let keys = Object.keys(data)
        let count = data[keys[0]].length
        
        for (let i = 0; i < count; i++) {
            let res = {}
            for (let key of keys) {
                res[key] = data[key][i]
            }
            sheet.content.push(res)
        }

        sheet.columns = keys.map(key => ({ label: key, value: key }))
        
        let settings = {
            fileName: `./output/${fileName}`,
            extraLength: 3,
            writeMode: "writeFile",
            writeOptions: {}
        }
        
        xlsx([sheet], settings)
        console.log(`结果已导出到: ${settings.fileName}.xlsx`)
    }
}

// 主函数
async function main() {
    const config: GAConfig = {
        populationSize: 100,    // 种群大小
        generations: 50,        // 进化代数
        crossoverRate: 0.8,     // 交叉概率
        mutationRate: 0.1,      // 变异概率
        eliteSize: 10,          // 精英保留数量
        teamSize: 5             // 阵容大小
    }

    const ga = new GeneticAlgorithmBattle(config)
    await ga.init()
    await ga.evolve()
}

// 运行程序
main().catch(console.error)
